<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  components: {
    NewConsignmentRentalValueAddedServiceOrdersDetail: () =>
      import('./detail.vue'),
    OldConsignmentRentalValueAddedServiceOrdersDetail: () =>
      import('./oldModel/detail.vue')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalValueAddedServiceOrdersDetail'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalValueAddedServiceOrdersDetail',
      'NewConsignmentRentalValueAddedServiceOrdersDetail'
    )
  }
}
</script>

<style lang="scss" scoped></style>
