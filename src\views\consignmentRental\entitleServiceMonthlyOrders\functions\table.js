import { showTime } from '@/utils/system/business'

/**
 * @description:权益服务（月付）订单列表数据
 */
export function setTableHeadArr() {
  return [
    { label: '子订单编码', prop: 'subOrderCode', minWidth: 220 },
    {
      label: '父订单编码',
      prop: 'orderCode',
      minWidth: 220,
      type: 'link',
      click: ({ row }) => {
        this.goDetail({
          value: row.orderCode,
          data: row,
          orderType: 0,
          isNew: true,
        })
      },
    },
    { label: '客户编码', prop: 'customerId', minWidth: 180 },
    { label: '客户名称', prop: 'customerName', minWidth: 220 },
    { label: '合同编码', prop: 'contractCode', minWidth: 300 },
    { label: '合同乙方', prop: 'secondPartyName', minWidth: 220 },
    { label: '应付金额', prop: 'payFee', minWidth: 110, type: 'money' },
    { label: '车牌号', prop: 'carNo', minWidth: 130 },
    { label: '车架号', prop: 'vinCode', minWidth: 220 },
    { label: '创建时间', prop: 'createTime', minWidth: 180 },
    { label: '订单状态', prop: 'orderStatusName', minWidth: 150 },
    {
      label: '订单有效期-开始时间',
      prop: 'validityBeginTime',
      minWidth: 170,
      formatter: ({ row }) => showTime([row.validityBeginTime])
    },
    {
      label: '订单有效期-结束时间',
      prop: 'validityEndTime',
      minWidth: 170,
      formatter: ({ row }) => showTime([row.validityEndTime])
    },
    {
      label: '操作',
      prop: 'options',
      fixed: 'right',
      minWidth: 80,
      type: 'options',
      list: [
        {
          text: '详情',
          click: ({ row }) =>
            this.goDetail({
              value: row.subOrderCode,
              data: row,
              orderType: row.orderType,
              isNew: true,
            }),
        },
      ],
    },
  ]
}
