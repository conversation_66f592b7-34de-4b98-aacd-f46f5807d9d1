import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取车辆加速抵扣费用订单列表数据
export function getCardepreciationOrderList(data) {
  return request({
    url: rentalUrl + '/cardepreciation/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询车辆加速抵扣费用订单基础信息
export function getCardepreciationOrderBasic(data) {
  return request({
    url: rentalUrl + '/cardepreciation/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
