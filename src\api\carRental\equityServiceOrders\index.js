import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取权益卡订单列表数据
export function getBenefitOrderList(data) {
  return request({
    url: rentalUrl + '/benefit/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取权益卡订单基础信息数据（根据code）
export function getBenefitOrderBasic(data) {
  return request({
    url: rentalUrl + '/benefit/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
