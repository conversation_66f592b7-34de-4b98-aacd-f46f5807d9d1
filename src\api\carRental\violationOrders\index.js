import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取违章代办订单列表数据
export function getViolationOrderList(data) {
  return request({
    url: rentalUrl + '/violation/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取违章代办订单列表数据
export function getViolationOrderBasic(data) {
  return request({
    url: rentalUrl + '/violation/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
