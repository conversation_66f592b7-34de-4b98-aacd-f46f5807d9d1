/**
 * @description:车辆定损订单列表数据
 */
export function setTableHeadArr() {
  return [
    { label: '订单编号', prop: 'bookingCode', minWidth: 200 },
    { label: '订单状态', prop: 'bookingStatusName', minWidth: 150 },
    { label: '订单金额（元）', prop: 'amount', minWidth: 150, type: 'money' },
    { label: '客户名称', prop: 'customerName', minWidth: 220 },
    { label: '客户编号', prop: 'customerId', minWidth: 180 },
    { label: '关联公司', prop: 'companyName', minWidth: 170 },
    { label: '业务归属', prop: 'saleName', minWidth: 170 },
    { label: '支付方式', prop: 'payMethodName', minWidth: 150 },
    { label: '创建时间', prop: 'createTime', minWidth: 200 },
    { label: '审核人', prop: 'approvalUserName', minWidth: 170 },
    { label: '审核时间', prop: 'approvalTime', minWidth: 200 },
    { label: '备注', prop: 'remark', minWidth: 220 },
    { label: '关联订单类型', prop: 'relationOrderTypeName', minWidth: 150 },
    { label: '关联订单号', prop: 'orderCode', minWidth: 200 },
    {
      label: '操作',
      prop: 'options',
      fixed: 'right',
      minWidth: 80,
      type: 'options',
      list: [
        {
          text: '查看附件',
          click: ({ row }) => {
            this.fileList = [
              {
                fileUrl: row.fileUrl || '',
                fileName: '附件',
              },
            ]
            this.viewFile = true
          },
        },
      ],
    },
  ]
}
