import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, serviceUrl } = common

// 获取受托预售订单列表数据
export function getEntrustPreSaleOrderList(data) {
  return request({
    url: serviceUrl + '/servicePerformance/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取受托预售订单基础信息数据（根据code）
export function getEntrustPreSaleOrderBasic(data) {
  return request({
    url: serviceUrl + '/servicePerformance/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
