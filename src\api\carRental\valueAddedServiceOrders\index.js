import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取增值服务订单列表数据
export function getValueAddOrderList(data) {
  return request({
    url: rentalUrl + '/valueAdded/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取增值服务订单基础信息数据（根据code）
export function getValueAddOrderBasic(data) {
  return request({
    url: rentalUrl + '/valueAdded/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
