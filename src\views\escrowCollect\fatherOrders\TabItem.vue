<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'escrowCollectFatherOrders',
  components: {
    NewEscrowCollectFatherOrders: () => import('./index.vue'),
    OldEscrowCollectFatherOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldEscrowCollectFatherOrders'
    }
  },
  created() {
    this.init('OldEscrowCollectFatherOrders', 'NewEscrowCollectFatherOrders')
  }
}
</script>

<style lang="scss" scoped></style>
