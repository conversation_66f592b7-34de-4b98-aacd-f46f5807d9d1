<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <dst-filtrate ref="DstFiltrate" :data-arr="setFiltrateData" @searchClick="searchClick"></dst-filtrate>
    <div class="dst-table-content">
      <sys-grid
        local-key="equity_services_father_orders"
        :is-loading="isLock"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        @pageChange="pageChange"
      />
    </div>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { setFiltrateData } from './functions/data'
import { setTableHeadArr } from './functions/table'
import mixin from '@/mixins/table.js'
import { queryOrderSearchParentItems } from '@/api/common/rental/parent'
import { changeVariableName } from '@/utils/business'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
import fullOrgTreesMixin from '@/views/common/common/mixins/serviceOrg.js'
import { getParentParams, setParentPageData } from '@/utils/system/business'
import orderMixins from '@/views/common/common/mixins/orderLink'
export default {
  name: 'NewEquityServiceFatherOrders',
  mixins: [mixin, selectCustomer, fullOrgTreesMixin, orderMixins],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 父订单总数
      paginationTotal: 0,
      // type当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5：代收款
      type: 2,
      // 首次不发送请求
      isNoneNeedFrist: true
    }
  },
  computed: {
    // 父订单搜索配置项
    setFiltrateData,
    // 父订单列表表头项
    setTableHeadArr
  },
  methods: {
    /**
     * @description: 初始化请求
     */
    init() {
      this.getTableData()
    },
    /**
     * @description:请求父订单列表数据
     */
    getTableData() {
      this.isLock = true
      const data = this.getParams()
      queryOrderSearchParentItems(data)
        .then(res => {
          this.paginationTotal = res.data.total || 0
          this.tableList = setParentPageData(res, false, this.orgInfo)
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:格式化列表请求参数
     */
    getParams() {
      const data = this.$batchInputUtil(this.setFiltrateData, this.searchData)
      changeVariableName(data, 'createTime', true)
      this.getCustomerIds(data, true)
      return {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        ...getParentParams({ data })
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import '~@/views/common/common/styles/table.scss';
</style>
