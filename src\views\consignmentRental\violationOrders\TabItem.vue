<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalViolationOrders',
  components: {
    NewConsignmentRentalViolationOrders: () => import('./index.vue'),
    OldConsignmentRentalViolationOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalViolationOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalViolationOrders',
      'NewConsignmentRentalViolationOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
