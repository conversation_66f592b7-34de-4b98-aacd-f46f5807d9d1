<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <dst-filtrate
      ref="DstFiltrate"
      :data-arr="setFiltrateData"
      :form-value="searchValue"
      @searchClick="searchClick"
    ></dst-filtrate>
    <div class="dst-table-content">
      <sys-grid
        local-key="consignment_rental_father_orders"
        row-key="orderCode"
        is-check-box
        :ref="isClearSelected"
        :is-loading="isLock"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        :toolbar-config="toolbarConfig"
        @pageChange="pageChange"
        @selection-change="handleSelectionChange"
      >
        <sys-button
          v-if="btnList.some((item) => !!item.show)"
          slot="toolbar_buttons"
          :btn-list="btnList"
          :limit="14"
        ></sys-button>
      </sys-grid>
    </div>
    <hand-car
      v-if="handCarFlag"
      :visible.sync="handCarFlag"
      :list="selectList"
      :customer-info="customerInfo"
      :type="type"
      @refresh="refresh"
    />
    <return-car
      v-if="returnCarFlag"
      :visible.sync="returnCarFlag"
      :list="selectList"
      :type="type"
      :station-list="stationList"
      @refresh="refresh"
    />
    <confirm-collect
      v-if="confirmCollectFlag"
      :visible.sync="confirmCollectFlag"
      :list="selectOrderCodes"
      :type="type"
      :mode="confirmCollectType"
      @refresh="refresh"
    />
    <invalid-dialog
      v-if="invaildFlag"
      :visible.sync="invaildFlag"
      :list="selectOrderCodes"
      :type="type"
      @refresh="refresh"
    />
    <modify-sales-belong
      v-if="saleBelongFlag"
      :visible.sync="saleBelongFlag"
      :list="selectOrderCodes"
      :type="type"
      @refresh="refresh"
    />
  </div>
</template>

<script>
import { setFiltrateData } from './functions/data'
import { btnList } from '@/views/common/parentOrder/dialog/list/functions/data.js'
import { setTableHeadArr } from './functions/table'
import mixin from '@/mixins/table.js'
import { changeVariableName, clearObjItem } from '@/utils/business'
import mutltipOptions from '@/views/common/parentOrder/dialog/list/mixins/mutltipOptions.js'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
import fullOrgTreesMixin from '@/views/common/common/mixins/serviceOrg.js'
import orderMixins from '@/views/common/common/mixins/orderLink'
import { isFunction } from '@/utils/types'
import { queryOrderSearchParentItems } from '@/api/common/rental/parent'
import { getParentParams, setParentPageData } from '@/utils/system/business'
import { getMouthData } from '@/utils/system'
export default {
  name: 'NewConsignmentRentalFatherOrders',
  components: {
    HandCar: () => import('@/views/common/parentOrder/dialog/list/HandCar.vue'),
    ReturnCar: () =>
      import('@/views/common/parentOrder/dialog/list/RefundCar.vue'),
    ConfirmCollect: () =>
      import('@/views/common/parentOrder/dialog/list/ConfirmCollect.vue'),
    InvalidDialog: () =>
      import('@/views/common/parentOrder/dialog/list/InvalidDialog.vue'),
    ModifySalesBelong: () =>
      import('@/views/common/parentOrder/dialog/list/ModifySaleBelong.vue')
  },
  mixins: [
    mixin,
    mutltipOptions,
    selectCustomer,
    fullOrgTreesMixin,
    orderMixins
  ],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 父订单总数
      paginationTotal: 0,
      // type当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5：代收款
      type: 1,
      // 当前选中的父订单数据
      selectList: [],
      // 是否自动清除复选项
      isClearSelected: 'sysTable',
      // 工具栏配置
      toolbarConfig: {
        slots: { buttons: 'toolbar_buttons' }
      },
      // 是否不需要首次主动加载
      isNoneNeedFrist: true
    }
  },
  computed: {
    // 父订单搜索配置项
    setFiltrateData,
    // 父订单列表表头项
    setTableHeadArr,
    // 父订单列表按钮配置
    btnList
  },
  methods: {
    /**
     * @description: 初始化请求
     */
    init() {
      this.getTableData()
    },
    /**
     * @description:请求父订单列表数据
     * @param {function} fn -请求回调
     */
    getTableData(fn) {
      this.isLock = true
      const data = this.getParams()
      queryOrderSearchParentItems(data)
        .then(res => {
          this.paginationTotal = res.data.total || 0
          this.tableList = setParentPageData(res, false, this.orgInfo)
        })
        .finally(() => {
          isFunction(fn) && fn()
          this.isLock = false
        })
    },
    /**
     * @description:格式化列表请求参数
     * @param {type}
     * @return:
     */
    getParams() {
      const params = this.$batchInputUtil(this.setFiltrateData, this.searchData)
      changeVariableName(params, 'createTime', true, 'Time')
      changeVariableName(params, 'realDeliveryTime', true, 'Time')
      changeVariableName(params, 'realBackCarTime', true, 'Time')
      this.getCustomerIds(params, true)
      const expLeaseMonth = getMouthData(params, 'expLeaseMonth')
      params.expLeaseStartMonth = expLeaseMonth.start
      params.expLeaseEndMonth = expLeaseMonth.end
      const realLeaseMonth = getMouthData(params, 'realLeaseMonth')
      params.realLeaseStartMonth = realLeaseMonth.start
      params.realLeaseEndMonth = realLeaseMonth.end
      return {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        ...getParentParams({
          data: clearObjItem(params),
          // rent-租赁、sale-售车、service-服务、rent_for_sale-以租代售
          orderBusinessType: 'rent_for_sale',
          orderTypeList: [this.type]
        })
      }
    },
    /**
     * @description:列表选中触发
     * @param {array} list 当前选中的列表数据
     */
    handleSelectionChange(list) {
      this.selectList = _.cloneDeep(list)
    },
    /**
     * @description: 重置数据集合
     */
    clickReset() {
      this.customerIds = []
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .filtrate-cont {
  height: 63px;
  &.open {
    padding-bottom: 10px;
  }
  .el-form-item {
    overflow: visible;
    .el-form-item__error {
      top: 45px;
    }
  }
}
</style>
