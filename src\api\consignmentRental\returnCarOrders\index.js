import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取退车定损订单列表数据
export function getBackcarlossOrderList(data) {
  return request({
    url: rentalSaleUrl + '/backcarloss/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取退车定损订单基础信息数据（根据code）
export function getBackcarlossOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/backcarloss/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
