import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取先租后买订单列表数据
export function getSaleforrentOrderList(data) {
  return request({
    url: rentalSaleUrl + '/saleforrent/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取先租后买订单基础信息数据（根据code）
export function getSaleforrentOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/saleforrent/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
