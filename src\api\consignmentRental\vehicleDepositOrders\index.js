import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取预付款订单列表数据
export function getVehicledepositOrderList(data) {
  return request({
    url: rentalSaleUrl + '/vehicledeposit/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取预付款订单基础信息数据（根据code）
export function getVehicledepositOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/vehicledeposit/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
