import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取过户订单列表数据
export function getAssignedOrderList(data) {
  return request({
    url: rentalSaleUrl + '/transfer/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取过户订单基础信息数据（根据id）
export function getAssignedOrderId(data) {
  return request({
    url: rentalSaleUrl + '/transfer/order/findByOrderId',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取过户订单基础信息数据（根据code）
export function getAssignedOrderCode(data) {
  return request({
    url: rentalSaleUrl + '/transfer/order/findByOrderCode',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
