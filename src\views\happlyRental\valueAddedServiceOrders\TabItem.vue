<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'happlyRentalValueAddedServiceOrders',
  components: {
    NewHapplyRentalValueAddedServiceOrders: () => import('./index.vue'),
    OldHapplyRentalValueAddedServiceOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldHapplyRentalValueAddedServiceOrders'
    }
  },
  created() {
    this.init(
      'OldHapplyRentalValueAddedServiceOrders',
      'NewHapplyRentalValueAddedServiceOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
