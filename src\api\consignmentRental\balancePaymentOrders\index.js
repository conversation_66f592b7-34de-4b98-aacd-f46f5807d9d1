import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取尾款订单列表数据
export function getRetainageOrderList(data) {
  return request({
    url: rentalSaleUrl + '/retainage/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取尾款订单基础信息数据（根据id）
export function getRetainageOrderId(data) {
  return request({
    url: rentalSaleUrl + '/retainage/order/findByOrderId',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取尾款订单基础信息数据（根据code）
export function getRetainageOrderCode(data) {
  return request({
    url: rentalSaleUrl + '/retainage/order/findByOrderCode',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
