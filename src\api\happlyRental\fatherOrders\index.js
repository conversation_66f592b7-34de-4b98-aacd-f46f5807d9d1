import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 创建父订单
export function createParentOrder(data) {
  return request({
    url: rentalSaleUrl + '/parent/order/create',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起换车(原车换替换车)
export function initiateChangeCarNew(data) {
  return request({
    url: rentalSaleUrl + '/parent/order/initiateChangeCar/new',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起换车(替换车换原车)
export function initiateChangeCarOld(data) {
  return request({
    url: rentalSaleUrl + '/parent/order/initiateChangeCar/old',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 替换车列表
export function replaceCarList(data) {
  return request({
    url: rentalSaleUrl + '/parent/order/replace',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 替换车转租赁
export function replaceCarToLease(data) {
  return request({
    url: rentalSaleUrl + '/replace/car/order/create',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
