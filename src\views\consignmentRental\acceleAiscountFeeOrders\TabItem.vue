<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalAcceleAiscountFeeOrders',
  components: {
    NewConsignmentRentalAcceleAiscountFeeOrders: () => import('./index.vue'),
    OldConsignmentRentalAcceleAiscountFeeOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalAcceleAiscountFeeOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalAcceleAiscountFeeOrders',
      'NewConsignmentRentalAcceleAiscountFeeOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
