<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalRenewalMarginOrders',
  components: {
    NewConsignmentRentalRenewalMarginOrders: () => import('./index.vue'),
    OldConsignmentRentalRenewalMarginOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalRenewalMarginOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalRenewalMarginOrders',
      'NewConsignmentRentalRenewalMarginOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
