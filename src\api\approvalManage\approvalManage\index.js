import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, tradeUrl, goodsUrl, bqmUrl } = common

// 获取审批管理列表数据（）
export function getApprovalManageList(data) {
  return request({
    url: tradeUrl + '/trade/approval/findDingDingProcessPage',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取审批管理列表数据（蓝凌OA）
export function getInnerInstancePage(data) {
  return request({
    url: goodsUrl + '/v1/bpm/page',
    method: 'post',
    data,
    hearderData,
  })
}

// 获取审批管理列表数据（蓝凌OA-es请求）
export function getInnerapiInstancePage(data) {
  return request({
    url: bqmUrl + '/innerapi/instance/page',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
