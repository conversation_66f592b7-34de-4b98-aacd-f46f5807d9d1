import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取权益服务订单列表数据
export function getEquityservicesOrderList(data) {
  return request({
    url: rentalUrl + '/equityservices/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询权益服务订单基础信息
export function getEquityservicesOrderBasic(data) {
  return request({
    url: rentalUrl + '/equityservices/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
