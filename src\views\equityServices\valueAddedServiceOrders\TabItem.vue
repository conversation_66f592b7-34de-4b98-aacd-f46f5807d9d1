<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'valueAddedServiceOrders',
  components: {
    NewValueAddedServiceOrders: () => import('./index.vue'),
    OldValueAddedServiceOrders: () => import('./oldModel'),
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: '',
    }
  },
  created() {
    this.init(
      'OldValueAddedServiceOrders',
      'NewValueAddedServiceOrders'
    )
  },
}
</script>

<style lang="scss" scoped></style>
