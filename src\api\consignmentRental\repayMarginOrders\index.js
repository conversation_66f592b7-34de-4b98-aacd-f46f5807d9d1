import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取还款保证金订单列表数据
export function getRepayMarginOrderList(data) {
  return request({
    url: rentalSaleUrl + '/repaymentdeposit/order/page',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取还款保证金订单基础信息数据（根据code）
export function getRepayMarginOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/repaymentdeposit/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
