var fs = require('fs');
var path = require('path');
var addPageFn = require('./template/add');
var tablePageFn = require('./template/table');
var detailPageFn = require('./template/detail');
var dataFn = require('./template/data');

let dataStr = dataFn()
// false  可以创建 true 不能创建
let isHas = false

function mkdirsSync(dirname) {
  if (fs.existsSync(dirname)) {
    return true;
  } else {
    if (mkdirsSync(path.dirname(dirname))) {
      fs.mkdirSync(dirname);
      return true;
    }
  }
}
// console.log(process.cwd(), 'process.cwd()')

function FileCreate(commonPath = '', arr) {
  arr.forEach(item => {
    const itemArr = item.split(' ')
    const VueName = itemArr[0].substring(itemArr[0].lastIndexOf('/') + 1)
    let pathItem = commonPath + itemArr[0]
    let paramObj = {
      'table': tablePageFn(VueName), // 表格
      'add': addPageFn(VueName), // 新增 编辑
      'detail': detailPageFn(VueName), // 详情
    }
    let template = paramObj[itemArr[1]]
    fs.exists(pathItem, function(exists) {
      isHas = exists
      console.log(!isHas)
      if (!isHas) {
        mkdirsSync(pathItem)
        fs.mkdirSync(`${pathItem}/data`, function(err) {
          if (err) {
            console.log('失败')
          } else {
            console.log('成功')
          }
        })

        fs.writeFileSync(`${pathItem}/index.vue`, template, function(err) {
          if (err) {
            return console.log(err);
          }
          console.log('The file was saved!');
        });
        // let JsJson = 'export default {\n}'
        fs.writeFileSync(`${pathItem}/data/index.js`, dataStr, function(err) {
          if (err) {
            return console.log(err);
          }
          console.log('The file was saved!');
        });
        console.log(`${pathItem},---新建成功---`)
      } else {
        console.log(`${pathItem},---文件已存在---`)
      }
    });
  })
}

function FileCreateOneFile(arr) {
  let url = process.env.INIT_CWD
  url = url.replace(process.cwd(), '')
  url = url.replace(/\\/g, '/');
  url = '.' + url

  if (arr.length == 1 && arr[0] == 'all') {
    ['t', 'd', 'a'].forEach(item => {
      file(url, item)
    })
  }
  arr.forEach(item => {
    file(url, item)
  })
}

function file(url, item) {
  let paramObj = {
    'table': tablePageFn('table'), // 表格
    'add': addPageFn('add'), // 新增 编辑
    'detail': detailPageFn('detail'), // 详情
  }
  let fileObj = {
    'table': 'index', // 表格
    'add': 'add', // 新增 编辑
    'detail': 'detail', // 详情
  }
  let fileName = fileObj[item]
  let template = paramObj[item]
  fs.exists(`${url}/${fileName}`, function(exists) {
    isHas = exists
    console.log(!isHas)
    if (!isHas) {
      mkdirsSync(`${url}/${fileName}`)
      fs.writeFileSync(`${url}/${fileName}/index.vue`, template, function(err) {
        if (err) {
          return console.log(err);
        }
        console.log('The file was saved!');
      });
      if (fileName === 'index') {
        fs.writeFileSync(`${url}/${fileName}/data.js`, dataStr, function(err) {
          if (err) {
            return console.log(err);
          }
          console.log('The file was saved!');
        });
      }
      console.log(`${url},---新建 ${fileName} 成功---`)
    } else {
      console.log(`${url},---文件已存在---`)
    }
  });
}
// module.exports = fileCreateOneFile

function FileCreateFileSrc(arr, commonPath) {
  let url = commonPath + arr[0]
  // console.log(url, commonPath, 'ddd')
  if (arr[1] == 'all') {
    ['table', 'detail', 'add'].forEach(item => {
      file(url, item)
    })
  } else {
    arr.forEach((i, idx) => {
      if (idx !== 0) {
        file(url, i)
      }
    })
  }

  // let url = process.env.INIT_CWD
  // url = url.replace(process.cwd(), '')
  // url = url.replace(/\\/g, '/');
  // url = '.' + url
  // let paramObj = {
  //   't': tablePageFn(), // 表格
  //   'a': addPageFn(), // 新增 编辑
  //   'd': detailPageFn(), // 详情
  // }
  // let fileObj = {
  //   't': 'table.vue', // 表格
  //   'a': 'add.vue', // 新增 编辑
  //   'd': 'detail.vue', // 详情
  // }
  // if (arr.length == 1 && arr[0] == 'all') {
  //   ['t', 'd', 'a'].forEach(item => {

  //   })
  // }
  // arr.forEach(item => {
  //   let fileName = fileObj[item]
  //   let template = paramObj[item]
  //   file(url, fileName, template)
  // })
}

module.exports = {
  FileCreate: FileCreate,
  FileCreateOneFile: FileCreateOneFile,
  FileCreateFileSrc: FileCreateFileSrc
}
