<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <dst-filtrate ref="DstFiltrate" :data-arr="setFiltrateData" @searchClick="searchClick"></dst-filtrate>
    <div class="dst-table-content">
      <sys-grid
        local-key="car_rental_convent_lease_orders"
        :is-loading="isLock"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        @pageChange="pageChange"
      />
    </div>
    <message-dialog :visible.sync="viewFlag" :view-type.sync="viewType"></message-dialog>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import { setFiltrateData } from './functions/data'
import { setTableHeadArr } from './functions/table'
import mixin from '@/mixins/table.js'
import { changeVariableName } from '@/utils/business'
import orderMixins from '@/views/common/common/mixins/orderLink'
import orderStatusMixins from '@/views/common/childOrders/mixins/orderStatus.js'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
import { fuzzyQueryGoodsName, queryOrderSearchSubItems } from '@/api/common/rental/childs.js'
import { getParams, setPageData } from '@/utils/system/business'
export default {
  name: 'NewCarRentalConventLeaseOrders',
  components: {
    MessageDialog: () => import('@/views/common/common/promptDialog')
  },
  mixins: [mixin, orderMixins, orderStatusMixins, selectCustomer],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 常用租赁订单总数
      paginationTotal: 0,
      // 弹出提示
      viewFlag: false,
      // 提示类型
      viewType: '',
      // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
      type: 0,
      // 商品搜索loading
      goodsLoad: false,
      // 获取商品名称列表
      goodsList: []
    }
  },
  computed: {
    // 常用租赁订单搜索配置项
    setFiltrateData,
    // 常用租赁订单列表表头项
    setTableHeadArr
  },
  methods: {
    /**
     * @description:请求常用租赁订单列表数据
     */
    getTableData() {
      this.isLock = true
      const data = this.getParams()
      queryOrderSearchSubItems(data)
        .then(res => {
          this.paginationTotal = res.data.total || 0
          this.tableList = setPageData(res)
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:远程搜索商品名称
     * @param {string} query 搜索字符
     */
    remoteMethodGoodsName(query) {
      if (query) {
        _.debounce(() => {
          this.goodsLoad = true
          fuzzyQueryGoodsName({ goodsName: query }, this.type)
            .then(res => {
              this.goodsList = (res.data || []).map(item => ({
                name: item
              }))
            })
            .catch(() => {
              this.goodsList = []
            })
            .finally(() => {
              this.goodsLoad = false
            })
        }, 200)()
      } else {
        this.goodsList = []
      }
    },
    /**
     * @description:格式化列表请求参数
     */
    getParams() {
      const params = this.$batchInputUtil(this.setFiltrateData, this.searchData)
      changeVariableName(params, 'createTime', true, 'Time')
      changeVariableName(params, 'expireStartTime', true)
      changeVariableName(params, 'expireEndTime', true)
      params.aggregation = params.aggregation ? '1' : '0'
      this.getCustomerIds(params, false)
      return {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        // 1-订单基础信息、2-客户信息、3-合同信息、4.履约信息、5-商品信息、6-优惠信息、7-赠送信息、8-销售信息、9-结算信息
        ...getParams({
          data: params,
          queryItems: [1, 2, 3, 4, 5],
          orderCategory: 1,
          orderBusinessType: 'rent'
        })
      }
    },
    /**
     * @description:点击问号弹出提示
     * @param {string} key 当前提示的区分 Agree:履约模块  Validity:有效期模块
     */
    view(key) {
      this.viewType = key
      this.viewFlag = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/views/common/common/styles/table.scss';
</style>
