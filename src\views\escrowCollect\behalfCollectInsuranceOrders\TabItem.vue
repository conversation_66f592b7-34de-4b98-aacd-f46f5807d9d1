<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'escrowCollectFatherOrders',
  components: {
    NewEscrowCollectBehalfCollectInsuranceOrders: () => import('./index.vue'),
    OldEscrowCollectBehalfCollectInsuranceOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldEscrowCollectBehalfCollectInsuranceOrders'
    }
  },
  created() {
    this.init(
      'OldEscrowCollectBehalfCollectInsuranceOrders',
      'NewEscrowCollectBehalfCollectInsuranceOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
