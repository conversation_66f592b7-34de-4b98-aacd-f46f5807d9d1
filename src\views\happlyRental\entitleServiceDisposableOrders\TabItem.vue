<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'happlyRentalEntitleServiceDisposableOrders',
  components: {
    NewHapplyRentalEntitleServiceDisposableOrders: () => import('./index.vue'),
    OldHapplyRentalEntitleServiceDisposableOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldHapplyRentalEntitleServiceDisposableOrders'
    }
  },
  created() {
    this.init(
      'OldHapplyRentalEntitleServiceDisposableOrders',
      'NewHapplyRentalEntitleServiceDisposableOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
