import request from '@/portal-common/api/request'
import apiUrl from '@/api/loginApi'

export function loginDD(params) {
  return request({
    baseURL: '',
    url: apiUrl.login.loginDD,
    method: 'post',
    data: {
      ...params
    }
  })
}

export function logout() {
  return request({
    baseURL: '',
    url: apiUrl.login.logout,
    method: 'post'
  })
}

export function getUserInfo(token) {
  return request({
    url: '/user/info',
    method: 'get',
    params: { token }
  })
}
