<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'happlyRentalBreachPenaltyOrders',
  components: {
    NewHapplyRentalBreachPenaltyOrders: () => import('./index.vue'),
    OldHapplyRentalBreachPenaltyOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldHapplyRentalBreachPenaltyOrders'
    }
  },
  created() {
    this.init(
      'OldHapplyRentalBreachPenaltyOrders',
      'NewHapplyRentalBreachPenaltyOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
