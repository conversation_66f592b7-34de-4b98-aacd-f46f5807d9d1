import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取权益服务（月付）订单列表数据
export function getEquityservicemonthpaymentOrderList(data) {
  return request({
    url: rentalSaleUrl + '/equityservicemonthpayment/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取权益服务（月付）订单基础信息数据（根据code）
export function getEquityservicemonthpaymentOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/equityservicemonthpayment/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
