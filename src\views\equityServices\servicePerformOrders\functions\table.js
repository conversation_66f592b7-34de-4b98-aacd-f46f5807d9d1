/**
 * @description:服务履约订单列表数据
 * @param {type}
 * @return:
 */
export function setTableHeadArr() {
  return [
    { label: '子订单编码', prop: 'subOrderCode', minWidth: 220 },
    {
      label: '父订单编码',
      prop: 'orderCode',
      minWidth: 220,
      type: 'link',
      click: ({ row }) => {
        this.goDetail({
          value: row.orderCode,
          data: row,
          orderType: 0,
          isNew: true
        })
      },
    },
    { label: '客户编码', prop: 'customerId', minWidth: 180 },
    { label: '客户名称', prop: 'customerName', minWidth: 220 },
    { label: '合同编码', prop: 'contractCode', minWidth: 300 },
    { label: '合同乙方', prop: 'secondPartyName', minWidth: 220 },
    {
      label: '应付金额',
      prop: 'totalAmount',
      minWidth: 150,
      type: 'money'
    },
    { label: '车牌号', prop: 'carNo', minWidth: 120 },
    { label: '车架号', prop: 'vinCode', minWidth: 170 },
    { label: '订单状态', prop: 'orderStatusName', minWidth: 120 },
    { label: '创建时间', prop: 'createTime', minWidth: 180 },
    { label: '履约方式', prop: 'performTypeName', minWidth: 170 },
    { label: '商品名称', prop: 'goodsName', minWidth: 170 },
    { label: '商品编码', prop: 'goodsCode', minWidth: 180 },
    {
      label: '操作',
      prop: 'options',
      fixed: 'right',
      minWidth: 80,
      type: 'options',
      list: [
        {
          text: '详情',
          click: ({ row }) =>
            this.goDetail({
              value: row.subOrderCode,
              data: row,
              orderType: row.orderType,
              isNew: true
            }),
        },
      ],
    },
  ]
}
