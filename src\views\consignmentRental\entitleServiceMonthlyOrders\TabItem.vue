<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'ConsignmentRentalEntitleServiceMonthlyOrders',
  components: {
    NewConsignmentRentalEntitleServiceMonthlyOrders: () =>
      import('./index.vue'),
    OldConsignmentRentalEntitleServiceMonthlyOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalEntitleServiceMonthlyOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalEntitleServiceMonthlyOrders',
      'NewConsignmentRentalEntitleServiceMonthlyOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
