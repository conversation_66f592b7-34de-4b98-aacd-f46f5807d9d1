<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <el-tabs class="dst-card-tabs" v-model="activeName" @tab-click="changeTab">
      <el-tab-pane v-for="item in tabList" :key="item.name" :label="item.label" :name="item.name">
        <component
          v-if="activeName === item.name && baseInfoForm"
          is-new
          :base-info-form.sync="baseInfoForm"
          :is="activeName"
          :ref="activeName"
          :type="type"
          :parent-id="parentId"
          :parent-code="parentCode"
          :customer-info-form="customerInfoForm"
          @parentRefresh="refresh"
        ></component>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import BaseInfo from './tabs/BaseInfo'
import commonTabs from '@/views/common/parentOrder/tabs'
import { setParentPageData } from '@/utils/system/business'
import { queryOrderSearchParentItem } from '@/api/common/rental/parent'
import { isFunction } from '@/utils/types'
import orderMixins from '@/views/common/common/mixins/orderLink'
import fullOrgTreesMixin from '@/views/common/common/mixins/serviceOrg.js'
export default {
  name: 'NewEscrowCollectFatherOrderDetail',
  components: { BaseInfo, ...commonTabs },
  mixins: [orderMixins, fullOrgTreesMixin],
  data() {
    return {
      // 加载对应的组件
      activeName: '',
      // 对应tabs的信息
      tabList: [
        { name: 'BaseInfo', label: '基础信息' },
        { name: 'EquityPrefer', label: '权益/优惠' },
        { name: 'AgreeModule', label: '履约信息' },
        { name: 'ChildsModule', label: '子订单信息' },
        { name: 'LogModule', label: '订单日志' }
      ],
      // 是否需要loading
      isLock: false,
      // 获取父订单详情数据
      baseInfoForm: null,
      // 获取父订单客户数据
      customerInfoForm: {}
    }
  },
  computed: {
    // 获取当前父订单id
    parentId() {
      return this.$route.query.id
    },
    // 获取当前父订单code
    parentCode() {
      return this.$route.query.code
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type() {
      return this.$route.query.type || 5
    }
  },
  created() {
    if (this.$route.query.name) {
      this.activeName = this.$route.query.name
    } else {
      this.activeName = 'BaseInfo'
    }
    this.tabList = this.isShowPortalLog()
      ? this.tabList
      : this.tabList.filter(item => !item.label.includes('日志'))
  },
  methods: {
    /**
     * @description:切换tab栏触发
     * @param {type}
     * @return:
     */
    changeTab() {
      this.goDetail({
        value: this.parentCode,
        data: this.baseInfoForm,
        orderType: 0,
        isNew: true,
        params: { name: this.activeName }
      })
    },
    /**
     * @description:刷新数据
     */
    refresh() {
      this.$nextTick(() => {
        this.init(() => {
          isFunction(this.$refs[this.activeName][0].refresh) &&
            this.$refs[this.activeName][0].refresh()
        })
      })
    },
    /**
     * @description:初始化基础信息数据
     * @param {function} fn 请求回调
     */
    init(fn) {
      this.isLock = true
      queryOrderSearchParentItem({
        // 1-订单基础信息、2-客户信息、3-合同信息、4.履约信息、5-商品信息、6-优惠信息、7-赠送信息（废弃）、8-销售信息、9-结算信息
        queryItems: [1, 2, 3, 4, 5, 6, 8],
        // rent-租赁、sale-售车、service-服务、rent_for_sale-以租代售
        orderBusinessType: 'service',
        orderBasic: { orderCode: this.parentCode }
      })
        .then(res => {
          const { baseInfo, customerInfo } = setParentPageData(
            res,
            true,
            this.orgInfo
          )
          this.baseInfoForm = _.cloneDeep(baseInfo)
          this.customerInfoForm = _.cloneDeep(customerInfo)
        })
        .finally(() => {
          isFunction(fn) && fn()
          this.isLock = false
        })
    }
  }
}
</script>
