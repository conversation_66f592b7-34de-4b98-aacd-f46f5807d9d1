import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取车辆加速折旧费订单列表数据
export function getCardepreciationOrderList(data) {
  return request({
    url: rentalSaleUrl + '/cardepreciation/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取车辆加速折旧费订单基础信息数据（根据code）
export function getCardepreciationOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/cardepreciation/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
