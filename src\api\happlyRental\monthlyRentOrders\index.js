import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取租金（月付）订单列表数据
export function getGeneralleaseOrderList(data) {
  return request({
    url: rentalSaleUrl + '/generallease/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询租金（月付）订单基础信息
export function getGeneralleaseOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/generallease/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
