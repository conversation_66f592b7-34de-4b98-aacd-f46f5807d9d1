<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalFatherOrders',
  components: {
    NewConsignmentRentalFatherOrders: () => import('./index.vue'),
    OldConsignmentRentalFatherOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalFatherOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalFatherOrders',
      'NewConsignmentRentalFatherOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
