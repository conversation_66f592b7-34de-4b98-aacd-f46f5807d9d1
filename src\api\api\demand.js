export default {
  getAllocationDemandListPage: '/operateBasic/allocation/demand/getAllocationDemandListPage',
  create: '/operateBasic/allocation/demand/create',
  loadCompanyOrg: '/operateBasic/allocation/demand/loadCompanyOrg',
  createInfo: '/operateBasic/allocation/demand/create',
  demandQuery: '/operateBasic/allocation/demand/query',
  getAllocationExecuteListPage: '/operateBasic/allocation/execute/getAllocationExecuteListPage',
  executeCreateInfo: '/operateBasic/allocation/execute/create',
  executeCreate: '/operateBasic/allocation/execute/create',
  loadCar: '/operateBasic/allocation/execute/loadCar',
  updateStatus: '/operateBasic/allocation/demand/updateStatus',
  demandStop: '/operateBasic/allocation/demand/stop',
  demandUpdate: '/operateBasic/allocation/demand/update',
  executeQuery: '/operateBasic/allocation/execute/query',
  queryIndex: '/operateBasic/allocation/execute/queryIndex',
  updateExecuteStatus: '/operateBasic/allocation/execute/updateExecuteStatus',
  executeStop: '/operateBasic/allocation/execute/stop',
  outTime: '/operateBasic/allocation/execute/outTime',
  inTime: '/operateBasic/allocation/execute/inTime',
  executeUpdate: '/operateBasic/allocation/execute/update',
  loadCarList: '/operateBasic/allocation/execute/loadCarList',
  allotExecutionSheetLogList: '/operateBasic/allocation/execute/loginfoList'
}
