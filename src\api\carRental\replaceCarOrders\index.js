import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取替换车订单列表数据
export function getReplaceCarOrderList(data) {
  return request({
    url: rentalUrl + '/replace/car/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取替换车订单列表数据
export function getReplaceCarOrderBasic(data) {
  return request({
    url: rentalUrl + '/replace/car/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
