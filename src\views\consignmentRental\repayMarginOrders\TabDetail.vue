<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  components: {
    NewConsignmentRentalRepayMarginOrderDetail: () => import('./detail.vue'),
    OldConsignmentRentalRepayMarginOrderDetail: () => import('./oldModel/detail.vue')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalRepayMarginOrderDetail'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalRepayMarginOrderDetail',
      'NewConsignmentRentalRepayMarginOrderDetail'
    )
  }
}
</script>

<style lang="scss" scoped></style>
