import { getSimpleDictByType } from '@/api/base'
import DstOrgSelector from '@/portal-common/components/DstOrgSelector/DstOrgSelector'
import { getOrgSplitList } from '@/api/common'
import dictionaries from '@/utils/common.js'
const { orderSource, customerGroupType } = dictionaries

/**
 * @description:父订单查询配置项
 */
export function setFiltrateData() {
  return [
    {
      type: 'input',
      prop: 'orderCode',
      label: '父订单编码',
      attrs: {
        placeholder: '请输入父订单编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'customerId',
      label: '客户编码',
      attrs: {
        placeholder: '请输入客户编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerKeys',
      label: '客户名称',
      width: '320px',
      attrs: {
        placeholder: '请输入客户名称',
        clearable: true,
        remote: true,
        multiple: true,
        filterable: true,
        reserveKeyword: true,
        remoteMethod: this.remoteCustomerMethod,
        loading: this.customerLoading,
      },
      options: this.customerList,
      ...this.filterSearchStyle,
      valueType: [],
      on: { change: (val, data) => this.handleChangeCustomer(data, true) },
    },
    {
      type: 'input',
      prop: 'orderChannel',
      label: '订单渠道',
      attrs: {
        placeholder: '请输入订单渠道',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'orderSource',
      label: '下单渠道',
      optionsApiConf: {
        parmas: { type: orderSource },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: DstOrgSelector,
      label: '服务组织',
      prop: 'regionIds',
      attrs: {
        // params: { startType: 'REGIONAL_LIST', endType: 'STATION_CITY' },
        customRequest: (vm) => {
          // 自定义接口,没有这个属性则用默认接口
          getOrgSplitList({
            startType: 'REGIONAL_LIST',
            endType: 'STATIONS_CITY',
            isAll: 1,
          }).then((res) => {
            // vm为orgSelector实例
            vm.optionsData = res.data || []
          })
        },
      },
      valueType: [],
      width: '310px',
      ...this.filterSearchStyle,
    },
    {
      type: 'cascader',
      prop: 'marketingOrgCodeItems',
      label: '营销组织',
      attrs: {
        placeholder: '请选择营销组织',
        clearable: true,
        // changeOnSelect: true,
        filterable: true,
        showAllLevels: false,
      },
      options: this.marketingOrgCodeItems,
      valueType: [],
      width: '310px',
      props: {
        value: 'code',
        label: 'name',
        children: 'childs',
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerGroupTypeList',
      label: '业务通路',
      optionsApiConf: {
        parmas: { type: customerGroupType },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      attrs: {
        placeholder: '请选择业务通路',
        clearable: true,
        multiple: true,
      },
      valueType: [],
      ...this.filterSearchStyle,
    },
    {
      type: 'DatePicker',
      prop: 'createTime',
      label: '创建时间',
      width: '400px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      valueType: [],
      labelWidth: '120px',
    },
  ]
}
