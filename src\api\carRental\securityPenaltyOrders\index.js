import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common
// 获取安全违约金订单列表数据
export function getSafetyliquidateddamagesOrderList(data) {
  return request({
    url: rentalUrl + '/safetyliquidateddamages/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询安全违约金订单基础信息
export function getSafetyliquidateddamagesOrderBasic(data) {
  return request({
    url: rentalUrl + '/safetyliquidateddamages/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
