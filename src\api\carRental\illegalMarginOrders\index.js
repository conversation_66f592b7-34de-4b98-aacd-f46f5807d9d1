import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取违章保证金订单列表数据
export function getViolationDepositOrderList(data) {
  return request({
    url: rentalUrl + '/violationDeposit/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 根据id获取违章保证金订单列表数据
export function getViolationDepositOrderId(data) {
  return request({
    url: rentalUrl + '/violationDeposit/order/id',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 根据code获取违章保证金订单列表数据
export function getViolationDepositOrderBasic(data) {
  return request({
    url: rentalUrl + '/violationDeposit/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
