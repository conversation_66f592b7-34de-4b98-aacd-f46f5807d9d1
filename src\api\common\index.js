import { request } from '@/portal-common/api/request'
import qs from 'qs'
import common from '@/utils/common.js'
const {
  storeUrl,
  stationUrl,
  goodsUrl,
  operationUrl,
  settleUrl,
  invoiceUrl,
  userUrl,
  hearderData,
  portalUrl,
  userVerCoreUrl
} = common

// 获取维保门店数据
export function getStoreList(data) {
  return request({
    url: storeUrl + `/operate/store/list`,
    method: 'post',
    data,
  })
}

// 获取所有退车原因列表
export function queryAllForChilds(data) {
  return request({
    url: stationUrl + `/oss/backReason/queryAllForChilds`,
    method: 'get',
    data,
  })
}

// 获取所有场实体站信息
export function getSimpleStations(data) {
  return request({
    url: stationUrl + `/openapi/station/getSimpleStations`,
    method: 'post',
    data,
  })
}

// 根据区域获得下属城市
export function getServeOrgsByParentId(data) {
  return request({
    url: operationUrl + '/serveorg/findServeOrgByParentId',
    method: 'get',
    data,
  })
}

// 获取大区-城市-场站三级联动 不包含已禁用的场站
export function getServiceOrgStationsByEnable(data) {
  return request({
    url: stationUrl + '/station/getServiceOrgStationsByEnable',
    method: 'get',
    data,
  })
}

// 获取所有场站信息
export function getStationList(data) {
  return request({
    url: stationUrl + '/station/getStationList',
    method: 'get',
    data,
  })
}

// 获取商品上架列表
export function shelfList(data) {
  return request({
    url: goodsUrl + `/goods/shelf/list`,
    method: 'get',
    data,
    paramsSerializer: (data) => {
      return qs.stringify(data, { indices: false })
    },
  })
}

// 根据定价价格ID查询所有商品
export function goodsPricelist(id) {
  return request({
    url: goodsUrl + `/contract/goods/price/list/${id}`,
    method: 'get',
  })
}

// 查询商品id列表查询商品参数信息
export function getGoodsParameterByGoodsIds(data, params) {
  return request({
    url: goodsUrl + `/good/getGoodsParameterByGoodsIds`,
    method: 'post',
    data,
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { indices: false })
    },
  })
}

// 获取合同商品详情上架规格笛卡尔积
export function goodsShelfDetail(data) {
  return request({
    url: goodsUrl + '/goods/shelf/contract/shelf/detail',
    method: 'get',
    data: data,
  })
}

// 获取场站商品的销售库存
export function getGoodsSalesStockByIdAndCityId(data) {
  return request({
    url: goodsUrl + '/goodsSale/getGoodsSalesStockById',
    method: 'get',
    data,
  })
}

// 资产公司列表
export function getCompanyListPage(data) {
  return request({
    url: operationUrl + '/company/getCompanyListPage',
    method: 'get',
    data,
  })
}

// 区域列表树
export function getAreaTree(data) {
  return request({
    url: operationUrl + '/area/getAreaTree',
    method: 'get',
    data,
  })
}

// 服务组织列表树
export function getServeOrgTree(data) {
  return request({
    url: operationUrl + '/serveorg/getServeOrgTree',
    method: 'get',
    data: data,
  })
}

// 获取场站商品的销售库存列表
export function getOperatingInventoryPageList(data) {
  return request({
    url: goodsUrl + '/goodsSale/getOperatingInventoryPageList',
    method: 'post',
    data,
  })
}

// 车辆订单-批量发起异常交车审批
export function mutiChangeGoodsBpm(data) {
  return request({
    url: goodsUrl + '/constractbpm/createChangeCarOrderGoodsBpm',
    method: 'post',
    data,
  })
}

// 通讯录
export function ddUserList(data) {
  return request({
    url: operationUrl + '/dd/user/list',
    method: 'post',
    data,
  })
}

// 根据大区获取运营公司
export function getOrgLinkage(data) {
  return request({
    url: operationUrl + '/org/getOrgLinkage',
    method: 'get',
    data,
  })
}

// 获取审批节点详情信息公共方法
export function getProcessByBusinessId({ data, url, baseUrl, method = 'get' }) {
  return request({
    url,
    method,
    data,
    baseURL: baseUrl,
    hearderData,
  })
}

// 搜索用户中心成员，同时带出所有信息
export function findDdUserAndAdminList(data) {
  return request({
    url: operationUrl + `/dd/user/findDdUserAndAdminList`,
    method: 'post',
    data,
  })
}

// 获取调剂类别
export function getAdjustCategoryConfigTypeList(data) {
  return request({
    url: settleUrl + '/adjustCategoryConfig/getAdjustCategoryConfigTypeList',
    method: 'get',
    data,
  })
}

// 获取订单类型配置的商品类目
export function getCategoryGoodsList(data) {
  return request({
    url: goodsUrl + '/category/goods/list',
    method: 'get',
    data,
  })
}

// 获取所有商品列表数据
export function getGoodsList(data) {
  return request({
    url: goodsUrl + '/good/getGoodsManagePage',
    method: 'post',
    data,
  })
}

// 获取商品规格
export function getSimpleModel(data) {
  return request({
    url: goodsUrl + '/goodsSale/getSimpleModel',
    method: 'get',
    data,
  })
}

// 获取所有的客户资料
export function getCustomerInfoListPage(data) {
  return request({
    url: goodsUrl + '/customerinfo/getCustomerInfoListPage',
    method: 'get',
    data,
  })
}

// 获取开票抬头信息
export function getInvoiceTitleByUserId(data) {
  return request({
    url: invoiceUrl + '/invoice/title/data/getByUserId',
    method: 'get',
    data,
  })
}

// 获取商品变更信息
export function getChangeGoodsInfo(data) {
  return request({
    url: goodsUrl + '/carorder/getChangeGoodsInfoList',
    method: 'post',
    data,
  })
}

// 获取商品库存数据
export function getGoodsSalesStockById(data) {
  return request({
    url: goodsUrl + '/goodsSale/getGoodsSalesStockById',
    method: 'get',
    data,
  })
}

// 车辆订单 - 批量异常交车
export function createChangeCarOrderGoodsBpm(data) {
  return request({
    url: goodsUrl + '/carorder/v2/createChangeCarOrderGoodsBpm',
    method: 'post',
    data,
  })
}

// 获取合同详情信息
export function getContractRentDetail(data) {
  return request({
    url: goodsUrl + '/contract/getRentContractBase',
    method: 'get',
    data,
  })
}

// 获取个人用户列表数据
export function getUserCenterListPage(data) {
  return request({
    url: userUrl + '/admin/getUserCenterListPage',
    method: 'post',
    data,
  })
}

// 获取企业用户列表数据
export function getUserEnterprisePage(data) {
  return request({
    url: operationUrl + '/enterprise/getUserEnterprisePage',
    method: 'post',
    data,
  })
}

// 获取用户列表数据(查询个人用户+企业用户列表)
export function getUserViewPage(data) {
  return request({
    url: operationUrl + '/userview/getUserViewPage',
    method: 'post',
    data,
  })
}

// 根据工号查询用户信息(包含部门)
export function findDdUserByJobNum(data) {
  return request({
    url: operationUrl + '/dd/user/findDdUserByJobNum',
    method: 'get',
    data,
  })
}

// 获取组织架构数据（某一节点）
// 大区：REGIONAL_LIST
// 运营城市：STATION_CITY
// 运营公司：OPERATING_COMPANY
// 场站城市：STATIONS_CITY
export function getOrgList(data) {
  return request({
    url: portalUrl + '/organization/list',
    method: 'get',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 截取开始到结束的二级树结构（组织架构）
export function getOrgSplitList(data) {
  return request({
    url: portalUrl + '/organization/list/split',
    method: 'get',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 获取商品快照信息
export function getMarketGoodsList(data) {
  return request({
    url: operationUrl + '/export/record/list',
    method: 'post',
    data,
  })
}

// 获取商户用户信息
export function getShopList(data) {
  return request({
    url: operationUrl + '/openapi/vendor/list',
    method: 'post',
    data,
    baseURL: process.env.BASE_API,
  })
}

// 获取完整运营组织树
export function getMarketingFullOrgTrees(data) {
  return request({
    url: operationUrl + '/openapi/orgvirtual/marketing/getFullOrgTrees',
    method: 'get',
    data,
    baseURL: process.env.BASE_API,
  })
}

// 查询条件查询组织列表
export function getMarketingList(data) {
  return request({
    url: operationUrl + '/openapi/orgvirtual/marketing/getList',
    method: 'post',
    data,
    baseURL: process.env.BASE_API,
  })
}

// 查询条件查询组织数据
export function getOrgByCode(data) {
  return request({
    url: operationUrl + '/openapi/orgvirtual/marketing/getOrgByCode',
    method: 'get',
    data,
    baseURL: process.env.BASE_API,
  })
}

// 用户中心用户列表
export function findInsideUserList(data) {
  return request({
    url: operationUrl + '/admin/findInsideUserList',
    method: 'post',
    data,
    baseURL: process.env.BASE_API,
  })
}

// 查询用户信息（包含曾用名，个人以及企业用户）
export function getUserInfoByName(data) {
  return request({
    url: userVerCoreUrl + '/usedName/getUserInfoByName',
    method: 'get',
    data,
  })
}
