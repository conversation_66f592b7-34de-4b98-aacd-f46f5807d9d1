import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取解约违约金订单列表数据
export function getBreachPenaltyOrderList(data) {
  return request({
    url: rentalSaleUrl + '/orderLiquidatedDamages/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取解约违约金订单基础信息数据（根据code）
export function getBreachPenaltyOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/orderLiquidatedDamages/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
