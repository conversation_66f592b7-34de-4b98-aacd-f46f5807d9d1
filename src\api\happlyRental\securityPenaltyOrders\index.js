import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取安全违约金订单列表数据
export function getSafetyliquidateddamagesOrderList(data) {
  return request({
    url: rentalSaleUrl + '/safetyliquidateddamages/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取安全违约金订单基础信息数据（根据code）
export function getSafetyliquidateddamagesOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/safetyliquidateddamages/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
