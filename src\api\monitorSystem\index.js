import request from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, monitorUrl } = common

// 查询app维度数据（监控数据）
export function monitorAppLogReportQuery(data, type) {
  return request.request({
    url: monitorUrl + '/monitorAppLogReport/' + type,
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询api维度数据（监控数据）
export function monitorApiLogReportQuery(data, type) {
  return request.request({
    url: monitorUrl + '/monitorApiLogReport/' + type,
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询日志配置所有数据 树结构（监控数据） --- 下拉查询
export function monitorApiInvokeLogConfQuery(data) {
  return request.request({
    url: monitorUrl + '/monitorApiInvokeLogConf/query',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询日志配置所有数据 树结构（监控数据） --- 列表展示
export function getMonitorConfigTree(data) {
  return request.request({
    url: monitorUrl + '/monitorApiInvokeLogConf/getMonitorConfigTree',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 修改配置状态
export function changeMonitorStatus(params, id) {
  return request({
    url: monitorUrl + '/monitorApiInvokeLogConf/changeStatus/' + id,
    method: 'put',
    params,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
