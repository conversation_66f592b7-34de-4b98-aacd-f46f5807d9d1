<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalBalancePaymentOrders',
  components: {
    NewConsignmentRentalBalancePaymentOrders: () => import('./index.vue'),
    OldConsignmentRentalBalancePaymentOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalBalancePaymentOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalBalancePaymentOrders',
      'NewConsignmentRentalBalancePaymentOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
