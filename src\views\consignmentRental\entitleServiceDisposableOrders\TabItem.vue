<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalEntitleServiceDisposableOrders',
  components: {
    NewConsignmentRentalEntitleServiceDisposableOrders: () =>
      import('./index.vue'),
    OldConsignmentRentalEntitleServiceDisposableOrders: () =>
      import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalEntitleServiceDisposableOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalEntitleServiceDisposableOrders',
      'NewConsignmentRentalEntitleServiceDisposableOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
