import { request } from '@/portal-common/api/request'
import apiUrl from '@/api/api'

// 字典类型列表 字典{type:'xxxx'}-java
export function getSimpleDictByType(data) {
  return request({
    url: apiUrl.common.getSimpleDictByType,
    method: 'get',
    data,
  })
}

// 数据字典-根据code精准查询列表-php
export function getDictionary(data) {
  return request({
    url: '/dst-mf' + '/basic-data/get-dictionary-list-by-code',
    method: 'post',
    baseURL: '/dst-apis/23',
    data,
  })
}
// 获取运力运营公司列表
export function getMfOperatorList(data) {
  return request({
    url: '/dst-mf' + '/basic-data/get-mf-operator-list',
    method: 'get',
    baseURL: '/dst-apis/23',
    data,
  })
}

// 用户中心，企业列表
export function getEnterpriseLegalList(data) {
  return request({
    url: '/dst-mf' + '/basic-data/get-enterprise-legal-list',
    method: 'post',
    baseURL: '/dst-apis/23',
    data,
  })
}
// 所有服务组织大区, 也可以通过参数{ level: 2 }获取所有大区下所有城市
export function getServeOrgByLevel(data) {
  return request({
    url: '/operateCore/serveorg/getServeOrgByLevel',
    method: 'GET',
    data,
  })
}
// ===== 上面的都是确认有使用的，下面的是store里要用的
// 通讯录
export function deptList(data) {
  return request({
    url: apiUrl.common.deptList,
    method: 'post',
    data,
  })
}

// 模糊查找用户
export function listPage(data) {
  return request({
    url: apiUrl.common.listPage,
    method: 'post',
    data,
  })
}

// 汪小松
export function getSimpleCurrentOrgs(data) {
  return request({
    url: apiUrl.common.getSimpleCurrentOrgs,
    method: 'get',
    data,
  })
}

export function getAreaLinkage(data) {
  return request({
    url: apiUrl.common.getAreaLinkage,
    method: 'get',
    data,
  })
}

// 短信验证
export function auth(data) {
  //
  return request({
    baseURL: '',
    url: apiUrl.common.auth,
    method: 'get',
    data,
  })
}
// 所有运营组织大区
export function getOrgAreaList(data) {
  return request({
    url: '/operateCore/org/getOrgAreaList',
    method: 'GET',
    data,
  })
}

// 获取当前用户服务组织的大区
export function getCurrentUserServeOrgArea(data) {
  return request({
    url: '/operateCore/serveorg/getCurrentUserServeOrgArea',
    method: 'get',
    data,
  })
}

// 品牌树
export function getBrandTypeTree(typeId) {
  return request({
    url: '/operateBasic/brandType/getBrandTypeTree',
    method: 'get',
    data: { typeId },
  })
}

export function getServeOrgsByParentId(data) {
  return request({
    url: '/operateCore/serveorg/findServeOrgByParentId',
    method: 'get',
    data,
  })
}

// 签名
export function uploadSign(data) {
  return request({
    url: '/operateCore/api/oss/uploadSign',
    method: 'get',
    data,
  })
}

// 合同乙方公司列表
export function getSimpleCompanyList(data) {
  return request({
    url: '/operateCore/company/getSimpleCompanyList',
    method: 'get',
    data,
  })
}

// 通讯录
export function ddUserList(data) {
  return request({
    url: apiUrl.configData.ddUserList,
    method: 'post',
    data,
  })
}

// 获取全局下载记录列表（假接口时使用）
export function recordList(data) {
  return request({
    url: '/operateCore/export/record/list',
    method: 'post',
    data,
  })
}

// 区域列表树
export function getAreaTree(data) {
  return request({
    url: '/operateCore/area/getAreaTree',
    method: 'get',
    data,
  })
}

// 服务组织列表树
export function getServeOrgTree(data) {
  return request({
    url: apiUrl.configData.getServeOrgTree,
    method: 'get',
    data,
  })
}

// 下拉区域列表树
export function getAreaTreeModel() {
  return request({
    url: '/operateCore/area/getAreaTreeModel',
    method: 'get',
  })
}
