<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'happlyRentalBalancePaymentOrders',
  components: {
    NewHapplyRentalBalancePaymentOrders: () => import('./index.vue'),
    OldHapplyRentalBalancePaymentOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldHapplyRentalBalancePaymentOrders'
    }
  },
  created() {
    this.init(
      'OldHapplyRentalBalancePaymentOrders',
      'NewHapplyRentalBalancePaymentOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
