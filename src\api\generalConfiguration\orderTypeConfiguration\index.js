import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, baseUrl } = common

// 新建订单类型配置
export function modifyOrderTypeconfigCreate(data, type) {
  return request({
    url: baseUrl(type) + '/config/order/typeconfig/create',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 订单类型配置列表
export function getOrderTypeConfigList(data, type) {
  return request({
    url: baseUrl(type) + '/config/order/typeconfig/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 修改订单类型配置
export function modifyOrderTypeconfigModify(data, type) {
  return request({
    url: baseUrl(type) + '/config/order/typeconfig/edit',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
