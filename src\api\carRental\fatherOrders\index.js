import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl, tradeUrl } = common

// 创建父订单
export function createParentOrder(data) {
  return request({
    url: rentalUrl + '/parent/order/create',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起换车(原车换替换车)
export function initiateChangeCarNew(data) {
  return request({
    url: rentalUrl + '/parent/order/initiateChangeCar/new',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起换车(替换车换原车)
export function initiateChangeCarOld(data) {
  return request({
    url: rentalUrl + '/parent/order/initiateChangeCar/old',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 替换车列表
export function replaceCarList(data) {
  return request({
    url: rentalUrl + '/parent/order/replace',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 替换车转租赁
export function replaceCarToLease(data) {
  return request({
    url: rentalUrl + '/replace/car/order/create',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 换车列表
export function transferCarList(data) {
  return request({
    url: rentalUrl + '/parent/openapi/search/getChangeCar',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 免租活动详情列表数据
export function getRentFreeActivityList(data) {
  return request({
    url: rentalUrl + '/parent/activity/rent/free',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 转租赁列表
export function getTransferLeaseCarList(data) {
  return request({
    url: rentalUrl + '/sublet/openapi/querySubletOriginalParentInfo',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 订单调整列表
export function getParentOrderAdjustOrderList(data) {
  return request({
    url: tradeUrl + '/trade/order/adjust/page/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
