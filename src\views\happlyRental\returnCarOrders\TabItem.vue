<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'happlyRentalReturnCarOrders',
  components: {
    NewHapplyRentalReturnCarOrders: () => import('./index.vue'),
    OldHapplyRentalReturnCarOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldHapplyRentalReturnCarOrders'
    }
  },
  created() {
    this.init(
      'OldHapplyRentalReturnCarOrders',
      'NewHapplyRentalReturnCarOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
