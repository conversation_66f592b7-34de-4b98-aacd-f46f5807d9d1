<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  components: {
    NewConsignmentRentalAcceleAiscountFeeOrderDetail: () =>
      import('./detail.vue'),
    OldConsignmentRentalAcceleAiscountFeeOrderDetail: () =>
      import('./oldModel/detail.vue')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldCarRentalAcceleAiscountFeeOrderDetail'
    }
  },
  created() {
    this.init(
      'OldCarRentalAcceleAiscountFeeOrderDetail',
      'NewCarRentalAcceleAiscountFeeOrderDetail'
    )
  }
}
</script>

<style lang="scss" scoped></style>
