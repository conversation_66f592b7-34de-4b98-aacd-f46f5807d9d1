module.exports = function addPageFn(name = '') {
  return `
<template>
  <div class="dst-add-card-wrap"
       v-loading="isLock">
    <el-form :model="ruleForm"
             :rules="rules"
             ref="ruleForm"
             label-width="140px"
             class="dst-add-overflow-y-box">
      <el-card v-for="item in [1,2,4]"
               :key="item">
        <div slot="header">
          <span class="dst-card-title">一定要记得根据自己的页面更改label-width</span>
        </div>
        <div class="dst-add-item-box">
          <el-form-item label="一行两个："
                        class="dst-add-form-item"
                        prop="aaaa">
            <el-input v-model="ruleForm.aaaa"
                      class="dst-add-input" />
          </el-form-item>
          <el-form-item class="dst-add-form-item"
                        label="一个lable多个输入"
                        prop="bbbb">
            <el-select v-model="ruleForm.bbbb"
                       class="dst-add-input"
                       clearable
                       filterable
                       placeholder="请选择">
              <el-option v-for="(item,index) in 4"
                         :key="index"
                         :label="'name'"
                         :value="'id'" />
            </el-select>
            <el-select v-model="ruleForm.bbbb"
                       class="dst-add-input"
                       clearable
                       filterable
                       placeholder="请选择">
              <el-option v-for="(item,index) in 4"
                         :key="index"
                         :label="'name'"
                         :value="'id'" />
            </el-select>
            <el-button>搜索</el-button>
          </el-form-item>
          <el-form-item class="dst-add-form-item"
                        label="日期区间:时分秒："
                        prop="datetimerange">
            <el-date-picker v-model="ruleForm.datetimerange"
                            type="datetimerange"
                            class="dst-add-input"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期" />
          </el-form-item>
          <el-form-item class="dst-add-form-item"
                        label="日期区间:时分秒："
                        prop="datetimerange">
            <el-date-picker v-model="ruleForm.datetimerange"
                            class="dst-add-input"
                            type="datetimerange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期" />
          </el-form-item>
          <el-form-item class="dst-add-form-item dst-block"
                        label="独占一行实例"
                        prop="datetimerange">
            <el-date-picker v-model="ruleForm.datetimerange"
                            type="datetimerange"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期" />
          </el-form-item>
          <el-form-item class="dst-add-form-item dst-block"
                        label="多文本示例："
                        prop="remark">
            <el-input class="dst-add-textarea-600px"
                      :rows="7"
                      v-model="ruleForm.datetimerange"
                      type="textarea"
                      placeholder="请输入任务摘要，字数限制10000字内" />
          </el-form-item>
        </div>
      </el-card>
    </el-form>
    <div class="dst-add-bottom-btn-box">
      <el-button type="success"
                 @click="submitForm">保存</el-button>
    </div>
  </div>
</template>
<script>
/* eslint-disable jsx-quotes */
export default {
  name: '',
  components: {},
  data() {
    return {
      isLock: false,
      ruleForm: {},
      rules: {
        aaaa: [
          {
            required: true,
            trigger: 'change'
          }
        ],
        bbbb: [{ required: true, trigger: 'change' }],
        cccc: [
          {
            required: true,
            trigger: 'blur'
          }
        ],
        dddd: [
          {
            required: true,
            trigger: 'blur'
          }
        ]
      }
    };
  },
  mounted() {},
  methods: {
    submitForm() {
      this.$refs.ruleForm.validate(valid => {
        if (valid) {
          this.isLock = true;
        } else {
          this.$message.error('请检查表单是否填写正确');
          return false;
        }
      });
    }
  }
};
</script>
<style lang="scss" scope>
</style>

`
}
