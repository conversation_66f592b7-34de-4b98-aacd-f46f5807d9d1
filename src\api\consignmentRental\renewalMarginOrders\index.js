import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取续保保证金订单列表数据
export function getRenewingCoverageOrderList(data) {
  return request({
    url: rentalSaleUrl + '/renewingCoverage/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取续保保证金订单基础信息数据（根据id）
export function getRenewingCoverageOrderId(data) {
  return request({
    url: rentalSaleUrl + '/renewingCoverage/order/findByOrderId',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取续保保证金订单基础信息数据（根据code）
export function getRenewingCoverageOrderCode(data) {
  return request({
    url: rentalSaleUrl + '/renewingCoverage/order/findByOrderCode',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
