import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const {
  hearderData,
  baseUrl,
  logUrl,
  operationUrl,
  goodsUrl,
  tradeUrl,
  userUrl,
  fulfillmentUrl,
  orderUrl,
} = common

// 获取父订单列表数据
export function getParentOrderList(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取父订单基础信息数据
export function getParentOrderBasic(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/basic',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取父订单优惠列表数据
export function getParentOrderCouponList(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/coupon/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取父订单赠送列表数据
export function getParentOrderGiveList(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/give/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取父订单客户信息数据
export function getParentOrderCustomer(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/customer',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取子订单列表数据
export function getParentOrderSubList(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/sub/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询订单日志
export function logList(data, type) {
  return request({
    url: logUrl(type) + `/log/list`,
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 修改客户信息
export function updateCustomerInfo(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/update/customer',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起交车
export function initiateDelivery(data, type) {
  return request({
    url:
      baseUrl(type) +
      '/parent' +
      (type == 0 ? '' : '/openapi/car/') +
      '/work/order/delivery',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起还车
export function initiateBackCar(data, type) {
  return request({
    url:
      baseUrl(type) +
      '/parent' +
      (type == 0 ? '' : '/openapi/car/') +
      '/work/order/back',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 作废交车
export function invalidDeliveryCar(data, type) {
  return request({
    url:
      baseUrl(type) +
      '/parent' +
      (type == 0 ? '' : '/openapi/car/') +
      '/work/order/invalid/delivery',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 作废还车
export function invalidReturnCar(data, type) {
  return request({
    url:
      baseUrl(type) +
      '/parent' +
      (type == 0 ? '' : '/openapi/car/') +
      '/work/order/invalid/backcar',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 作废车辆订单
export function invalidCar(data, type) {
  return request({
    url:
      baseUrl(type) +
      '/parent' +
      (type == 0 ? '' : '/openapi/car/') +
      '/order/invalid',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 取消订单
export function cancelOrder(data, type) {
  return request({
    url:
      baseUrl(type) +
      '/parent' +
      (type == 0 ? '' : '/openapi/') +
      '/order/cancel',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 管理备注
export function manageNotes(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/manage/notes',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询父订单交车信息
export function getParentOrderDelivery(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/delivery',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询父订单还车信息
export function getParentOrderBackcar(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/backcar',
    method: 'get',
    data,
    type,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询违章信息
export function getParentOrderViolationList(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/violation/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查看过户信息
export function getParentOrderTransferList(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/transfer',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询父订单履约还车信息强收时间
export function getForcecarOrderTime(data, type) {
  return request({
    url: baseUrl(type) + '/forcecar/order/time/search',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询父订单合同信息
export function getContractList(data, code, type) {
  return request({
    url: baseUrl(type) + '/contract/listByContractCode/' + code,
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 下发交车工单
export function appiontHandCar(data, type) {
  return request({
    url:
      baseUrl(type) +
      (type == 1
        ? '/parent/order/batchDeliveryCar'
        : '/parent/work/order/batchDeliveryCar'),
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 修改销售归属
export function modifySaleBelong(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/updateSaleman',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 下发退车工单
export function appiontReturnCar(data, type) {
  return request({
    url:
      baseUrl(type) +
      (type == 1
        ? '/parent/order/batchBackCar'
        : '/parent/work/order/batchBackCar'),
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 校验并获取提车客户信息数据
export function checkPickCustomer(data, type) {
  return request({
    url:
      baseUrl(type) +
      (type == 1
        ? '/parent/order/checkOrderCarService'
        : '/parent/work/order/checkOrderCarService'),
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 变更续租合同主体
export function createRenewalCarOrdersProcess(data) {
  return request({
    url: operationUrl + '/export/record/list',
    method: 'post',
    data,
  })
}

// 获取客户的退车申请
export function getCustomerBackCarApply(data) {
  return request({
    url: goodsUrl + '/backcar/getCustomerBackCarApply',
    method: 'post',
    data,
  })
}

// 提车客户
export function getCustomerInfoAndInvoiceList(data) {
  return request({
    url: goodsUrl + '/customerinfo/getCustomerInfoAndInvoiceList',
    method: 'get',
    data,
  })
}

// 车辆订单续租，加载可续租合同
export function getOrderListByReviewed(data) {
  return request({
    url: goodsUrl + '/contract/getOrderListByReviewed',
    method: 'get',
    data,
  })
}

// 搜索车辆订单
export function selectRenewalCarOrders(data, type) {
  return request({
    url:
      baseUrl(type) + '/parent/order/selectRenewalCarOrdersInfoInOrderCenter',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 车辆续租
export function renewalCarOrders(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/renewalRentOrders',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取父订单下的子订单数据
export function findFirstPayParentOrderCode(data, type) {
  return request({
    url: baseUrl(type) + '/parent/paid/findFirstPay',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 确认收款
export function confirmCollect(data, type) {
  return request({
    url: baseUrl(type) + '/parent/paid/confirm',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 计算父订单首付款总金额
export function getTotalAmount(data, type) {
  return request({
    url: baseUrl(type) + '/parent/paid/totalAmount',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取合同信息
export function getParentOrderContractGoodsList(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/contract/goods/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取轧差订单列表
export function getParentOrderAdjustOrderList(data, type) {
  return request({
    url: baseUrl(type) + '/orderAdjust/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 作废主订单
export function invalidOrders(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/invalidOrder',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 获取子订单状态
export function getSubOrderStatus(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/getOrderStatus',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 搜索转续租订单
export function getSubletOrderList(data) {
  return request({
    url: tradeUrl + '/trade/order/sublet/getOrderList',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起续租
export function handleReletSubmit(data) {
  return request({
    url: tradeUrl + '/trade/order/sublet/subletCheck',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起转租审批
export function approvalSublease(data) {
  return request({
    url: tradeUrl + '/trade/approval/sublease/submit',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起退车审批（）
export function approvalDDBackCar(data, type) {
  return request({
    url:
      tradeUrl +
      (type == 0
        ? '/trade/approval/backCar/submit'
        : '/trade/rentsale/approval/backCar/submit'),
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 退车查询车辆信息
export function getBasicCarTranslate(data, type) {
  return request({
    url: baseUrl(type) + '/backcar/order/getBasicCarTranslate',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取事件类型
export function getParentOrderType(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/type',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取商户合作信息
export function getSubInfo(data, type) {
  return request({
    url: userUrl + '/vendor/vendorDetail/' + data,
    method: 'get',
    hearderData,
    baseURL: process.env.BASE_API,
  })
}

// 批量发送提车码短信
export function verificationCode(data, type) {
  return request({
    url: baseUrl(type) + '/parent/verificationCode/sms',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 过户确认收款子订单列表
export function getSubOrderAmount(data, type) {
  return request({
    url: baseUrl(type) + '/parent/paid/subOrderAmount',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起过户确认收款
export function transferConfirm(data, type) {
  return request({
    url: baseUrl(type) + '/parent/paid/transferConfirm',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 修改提车场站
export function changeStation(data, type) {
  return request({
    url: baseUrl(type) + '/parent/order/update/station',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 租赁确认收款（对接交易）
export function tradeConfirmCollect(data) {
  return request({
    url: tradeUrl + '/trade/lease/order/business/confirm',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 租赁批量交车（对接交易）
export function tradeAppiontHandCar(data) {
  return request({
    url: tradeUrl + '/trade/open/appointment/lease/delivery/batchDeliveryCar',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 租赁批量退车（对接交易） - C端
export function tradeAppiontReturnCar(data) {
  return request({
    url: tradeUrl + '/trade/open/appointment/lease/backcar/batchBackCar',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 判断灰度用户操作（对接交易）
export function isTradeGrayJudge(data) {
  return request({
    url: tradeUrl + '/trade/gray/grayJudge',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取商户详情数据
export function getSubMerchentVendorItem(data) {
  return request({
    url: operationUrl + '/newVendor/vendorDetailByVenderId',
    method: 'post',
    data,
  })
}

// 获取商户门店详情
export function getMchStoreItem(data) {
  return request({
    url: operationUrl + '/newVendor/getShopDetail',
    method: 'get',
    data,
  })
}

// 获取父订单履约信息列表
export function getParentOrderPerformList(data, type) {
  return request({
    url:
      fulfillmentUrl + '/openapi/fulfillment/car/queryDeliverBillByOrderCode',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API,
  })
}

// 获取当前父订单下所有子订单的履约信息列表
export function getParentOrderSubPerformItems(data, type) {
  const url = [2, 3, 5].includes(Number(type))
    ? '/servicePerformance'
    : '/parent'
  return request({
    url: baseUrl(type) + url + '/order/sub/fulfillment/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 发起退车审批（蓝凌OA）
export function approvalLLBackCar(data, type) {
  const urlInfo = {
    0: '/landray/trade/approval/leaseBackProcess',
    1: '/landray/trade/approval/rentToBuyBackProcess',
    4: '/landray/trade/approval/happyRentBackCarProcess',
  }
  const url = urlInfo[type] || urlInfo[1]
  return request({
    url: goodsUrl + url,
    method: 'post',
    data,
    hearderData,
  })
}

// 查询主订单列表信息
export function queryOrderSearchParentItems(data) {
  return request({
    url: orderUrl + '/order/search/basic/page',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 查询主订单详情信息
export function queryOrderSearchParentItem(data) {
  return request({
    url: orderUrl + '/order/search/basic',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}
