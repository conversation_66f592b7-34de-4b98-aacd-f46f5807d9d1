import { getSimpleDictByType } from '@/api/base'
import dictionaries from '@/utils/common.js'
const { IS_NEW_FLAG } = dictionaries

export default {
  data() {
    return {
      // 是否需要loading
      isLock: false,
    }
  },
  methods: {
    /**
     * 初始化组件激活标签页名称
     *
     * 逻辑流程：
     * 1. 优先从路由查询参数中获取标签页名称
     * 2. 若路由参数不存在，则通过字典接口获取系统配置
     * 3. 根据字典配置决定使用新/旧版标签页组件
     * 4. 最终设置组件渲染标识并解除锁定状态
     *
     * @param {string} oldName - 旧版组件名称（函数内未使用）
     * @param {string} newName - 新版组件名称（函数内未使用）
     */
    init(oldName, newName) {
      if (this.$route.query.isNewFlag) {
        const targetName = this.$route.query.isNewFlag == 1 ? newName : oldName
        this.activeName = targetName
      } else {
        this.isLock = true
        getSimpleDictByType({ type: IS_NEW_FLAG })
          .then((res) => {
            const isNewFlagList = res.data || []
            let targetName = oldName
            if (!this.$isEmpty(isNewFlagList)) {
              const isNewFlagItem = isNewFlagList[0]
              const isNewFlag = isNewFlagItem.id
              targetName = isNewFlag == 1 ? newName : oldName
            }
            // 只在需要改变时才重新赋值，避免不必要的组件重新渲染
            if (this.activeName !== targetName) {
              this.activeName = targetName
            }
          })
          .catch(() => {
            // 只在需要改变时才重新赋值，避免不必要的组件重新渲染
            if (this.activeName !== oldName) {
              this.activeName = oldName
            }
          })
          .finally(() => {
            this.isLock = false
          })
      }
    },
  },
}
