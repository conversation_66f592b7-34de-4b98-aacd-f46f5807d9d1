module.exports = function dataFn(name = '') {
  return `
export default function() {
  let v = this
  return {
    filtrate_data: [
      // filtrate_data
      {
        prop: 'courseName',
        label: '输入框',
        type: 'input',
        clearable: true,
      },
      {
        prop: 'classifyId',
        label: '下拉框',
        type: 'select',
        options: [{
          label: 'A类',
          value: 1
        },
        {
          label: 'B类',
          value: 2
        }]
      },
      {
        prop: 'Date',
        label: '创建时间',
        type: 'daterange'
      },
      {
        prop: 'courseName1',
        label: '两个输入框',
        type: 'inputRange',
        clearable: true,
      },
      {
        prop: 'Date1',
        label: '时间范围',
        type: 'datetimerange'
      }
    ],
    headArray: [
      //
      {
        label: '固件名称',
        prop: 'carVin',
        minWidth: 200
      },
      {
        label: '固件版本号',
        prop: 'carVin',
        minWidth: 200
      },
      {
        label: '发版日期',
        prop: 'carVin',
        minWidth: 200
      },
      {
        label: '适用设备',
        prop: 'carVin',
        minWidth: 200
      },
      {
        label: '适用品牌',
        prop: 'carVin',
        minWidth: 200
      },
      {
        label: '适用型号',
        prop: 'carVin',
        minWidth: 200
      },
      {
        label: '固件状态',
        prop: 'carVin',
        minWidth: 200
      },
      {
        label: '添加时间',
        prop: 'carVin',
        minWidth: 200
      },
      {
        label: '操作人',
        prop: 'carVin',
        minWidth: 200
      },
      {
        label: '操作',
        prop: 'carVin',
        fixed: 'right',
        width: 140,
        render: (h, scope) => {
          const list = [
            {
              value: '编辑',
              click: v.editFn.bind(null, scope.row),
              // isShow: v.isAuth('oss:sale:customer:editcustomer')
            },
            {
              value: '删除',
              click: v.deleteFn.bind(null, scope.row),
              // isShow: v.isAuth('oss:sale:customer:editcustomer')
            },
          ]
          return (<dst-btns btnList={list}></dst-btns>)
        }
      },
    ],
    tableList: [],
    configTable: {
      pageNum: 1,
      pageSize: 20,
      isAutoHeight: true,
      isLock: false,
      isSerialNum: true,
      total: 0
    },
    searchData: {},
  }
}
`
}
