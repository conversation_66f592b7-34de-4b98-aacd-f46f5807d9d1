import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取强制收车服务费订单列表数据
export function getForcecarOrderList(data) {
  return request({
    url: rentalSaleUrl + '/forced/order/page',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 根据code获取强制收车服务费订单列表数据
export function getForcecarOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/forced/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
