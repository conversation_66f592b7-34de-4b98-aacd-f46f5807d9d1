<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalEquityServiceOrders',
  components: {
    NewConsignmentRentalEquityServiceOrders: () => import('./index.vue'),
    OldConsignmentRentalEquityServiceOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalEquityServiceOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalEquityServiceOrders',
      'NewConsignmentRentalEquityServiceOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
