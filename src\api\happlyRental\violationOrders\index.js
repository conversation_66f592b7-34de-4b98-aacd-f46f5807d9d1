import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取违章代办订单列表数据
export function getViolationOrderList(data) {
  return request({
    url: rentalSaleUrl + '/violation/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取违章代办订单基础信息数据（根据id）
export function getViolationOrderId(data) {
  return request({
    url: rentalSaleUrl + '/violation/order/findByOrderId',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取违章代办订单基础信息数据（根据code）
export function getViolationOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/violation/order/findByOrderCode',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
