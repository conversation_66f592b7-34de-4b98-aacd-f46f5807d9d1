import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, fulfillmentUrl } = common

// 获取当前父订单下所有子订单的履约信息列表（服务履约）
export function getServiceParentOrderPerformList(data, type) {
  return request({
    url: fulfillmentUrl + '/openapi/fulfillment/car/queryTraceOrderByOrderCode',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API,
  })
}

