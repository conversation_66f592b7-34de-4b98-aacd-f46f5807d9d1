import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, baseUrl, orderUrl } = common

// 查询订单商品信息
export function getSubOrderGoodsList(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/goods/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取异常交车商品信息
export function getSubOrderChangeGoodsList(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/changeGoods/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询订单费用信息
export function getSubOrderCostList(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/coupon/list',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 查询订单金额记录信息
export function getOrderfeeadjustrecordList(data, type) {
  return request({
    url: baseUrl(type) + '/fee/adjust/record/openapi/list',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 查询订单赠送信息
export function getSubOrderGiveList(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/give/list',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 获取订单抵扣信息
export function getSubOrderDeductionList(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/deduction/list',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 获取订单履约信息
export function getSubOrderPerformList(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/perform/list',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 获取订单结算信息
export function getSubOrderSettlementInfo(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/settlement',
    method: 'get',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 获取价格记录列表
export function getSubOrderPriceList(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/price/list',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 确认收款
export function getSubOrderConfirm(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/confirm',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 确认退款
export function getSubOrderConfirmRefund(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/confirmRefund',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 创建价格修改记录
export function createOrderPrice(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/price/create',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 接受确认收款
export function getSubOrderApproval(data, type) {
  return request({
    url: baseUrl(type) + '/sub/order/approval',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 模糊搜索车牌号
export function fuzzyQueryCarNo(data, type) {
  return request({
    url: baseUrl(type) + '/generallease/order/fuzzyQueryCarNo',
    method: 'get',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 模糊搜索客户名称
export function fuzzyQueryCustomerName(data, type) {
  return request({
    url: baseUrl(type) + '/generallease/order/fuzzyQueryCustomerName',
    method: 'get',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 模糊搜索商品名称
export function fuzzyQueryGoodsName(data, type) {
  return request({
    url: baseUrl(type) + '/generallease/order/fuzzyQueryGoodsName',
    method: 'get',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 查询子订单列表信息
export function queryOrderSearchSubItems(data) {
  return request({
    url: orderUrl + '/order/search/sub/page',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 查询单个子订单信息
export function queryOrderSearchSubItem(data) {
  return request({
    url: orderUrl + '/order/search/sub',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}
