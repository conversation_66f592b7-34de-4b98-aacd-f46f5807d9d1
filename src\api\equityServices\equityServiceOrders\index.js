import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, serviceUrl } = common

// 获取权益服务订单列表数据
export function getBenefitcardOrderList(data) {
  return request({
    url: serviceUrl + '/benefitcard/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取权益服务订单基础信息数据（根据code）
export function getBenefitcardOrderBasic(data) {
  return request({
    url: serviceUrl + '/benefitcard/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
