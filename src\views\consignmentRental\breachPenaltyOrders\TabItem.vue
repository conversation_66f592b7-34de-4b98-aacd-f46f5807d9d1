<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalBreachPenaltyOrders',
  components: {
    NewConsignmentRentalBreachPenaltyOrders: () => import('./index.vue'),
    OldConsignmentRentalBreachPenaltyOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldCarRentalAcceleAiscountFeeOrders'
    }
  },
  created() {
    this.init('OldCarRentalAcceleAiscountFeeOrders', 'NewCarRentalAcceleAiscountFeeOrders')
  }
}
</script>

<style lang="scss" scoped></style>
