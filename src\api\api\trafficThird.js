import { getToken } from '@/portal-common/utils/auth'
import myaxios from 'axios'
export const api = process.env.BASE_API6
const instance = myaxios.create({
  // baseURL: '/dst-apis/1/t-mtc-apis',
  baseURL: api,
  timeout: 120000
})
instance.interceptors.request.use(config => {
  // console.log("config==>",config);
  // config.headers['Dst-Cookie'] = getToken()
  config.headers['token'] = getToken()
  config.headers['withCredentials'] = true
  config.method = 'post'
  return config
}, error => {
  // console.log("error==>",error);
  return Promise.reject(error)
})
export default instance
