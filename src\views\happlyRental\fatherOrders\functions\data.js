import { getSimpleDictByType } from '@/api/base'
import batchInput from '@/views/common/common/batchSearch'
import DstOrgSelector from '@/portal-common/components/DstOrgSelector/DstOrgSelector'
import { monthGroupList, orderCustomerTypeList } from '@/utils/system/constant'
import { getOrgSplitList } from '@/api/common'
import dictionaries from '@/utils/common.js'
const {
  orderSource,
  isBond,
  carOrderVehicleUse,
  carStatus,
  customerGroupType,
} = dictionaries

/**
 * @description:父订单查询配置项
 * @param {type}
 * @return:
 */
export function setFiltrateData() {
  return [
    {
      type: batchInput,
      prop: 'orderCodeList',
      label: '父订单编码',
      attrs: {
        placeholder: '请输入父订单编码',
        clearable: true,
      },
      width: '350px',
      initValue: this.sourceInfo.parentOrderCode || '',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'orderStatus',
      label: '订单状态',
      optionsApiConf: {
        parmas: { type: carStatus },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      initValue: this.sourceInfo.carStatus || '',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerType',
      label: '客户类型',
      options: orderCustomerTypeList,
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerKeys',
      label: '客户名称',
      width: '320px',
      attrs: {
        placeholder: '请输入客户名称',
        clearable: true,
        remote: true,
        multiple: true,
        filterable: true,
        reserveKeyword: true,
        remoteMethod: this.remoteCustomerMethod,
        loading: this.customerLoading,
      },
      options: this.customerList,
      ...this.filterSearchStyle,
      valueType: [],
      on: { change: (val, data) => this.handleChangeCustomer(data, true) },
    },
    {
      type: 'input',
      prop: 'contractCodes',
      label: '合同编码',
      attrs: {
        placeholder: '请输入合同编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: batchInput,
      prop: 'carNoBatch',
      label: '车牌号',
      attrs: {
        placeholder: '请输入车牌号',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'vinCode',
      label: '车架号',
      attrs: {
        placeholder: '请输入车架号',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'carUse',
      label: '车辆用途',
      optionsApiConf: {
        parmas: { type: carOrderVehicleUse },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'saleName',
      label: '业务归属',
      attrs: {
        placeholder: '请输入业务归属',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'orderChannel',
      label: '订单渠道',
      attrs: {
        placeholder: '请输入订单渠道',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'orderSource',
      label: '下单渠道',
      optionsApiConf: {
        parmas: { type: orderSource },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: DstOrgSelector,
      label: '服务组织',
      prop: 'regionIds',
      attrs: {
        customRequest: (vm) => {
          getOrgSplitList({
            startType: 'REGIONAL_LIST',
            endType: 'STATIONS_CITY',
            isAll: 1,
          }).then((res) => {
            vm.optionsData = res.data || []
          })
        },
      },
      valueType: [],
      width: '310px',
      ...this.filterSearchStyle,
    },
    {
      type: 'cascader',
      prop: 'marketingOrgCodeItems',
      label: '营销组织',
      attrs: {
        placeholder: '请选择营销组织',
        clearable: true,
        filterable: true,
        showAllLevels: false,
      },
      options: this.marketingOrgCodeItems,
      valueType: [],
      width: '310px',
      props: { value: 'code', label: 'name', children: 'childs' },
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerGroupTypeList',
      label: '业务通路',
      optionsApiConf: {
        parmas: { type: customerGroupType },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      attrs: {
        placeholder: '请选择业务通路',
        clearable: true,
        multiple: true,
      },
      valueType: [],
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'depositFlag',
      label: '保证金',
      optionsApiConf: {
        parmas: { type: isBond },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'expLeaseMonth',
      label: '预计租赁时长（月）',
      labelWidth: '150px',
      width: '300px',
      options: monthGroupList,
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'realLeaseMonth',
      label: '实际租赁时长（月）',
      labelWidth: '150px',
      width: '300px',
      options: monthGroupList,
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'businessType',
      label: '业务类型',
      labelWidth: '100px',
      width: '300px',
      options: [
        { name: '欢乐租', id: 0 },
        { name: '更欢乐', id: 1 },
      ],
      ...this.filterSearchStyle,
    },
    {
      type: 'DatePicker',
      prop: 'createTime',
      label: '创建时间',
      width: '400px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      valueType: [],
      labelWidth: '120px',
    },
    {
      type: 'DatePicker',
      prop: 'realDeliveryTime',
      label: '实际交车时间',
      width: '400px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      valueType: [],
      labelWidth: '120px',
    },
    {
      type: 'DatePicker',
      prop: 'realBackCarTime',
      label: '实际还车时间',
      width: '400px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      valueType: [],
      labelWidth: '120px',
    },
  ]
}
