const webpack = require('webpack')
const path = require('path')

// 需要独立打包的第三方库，自行添加
let vendors = [
  'vue', 
  'vue-router', 
  'element-ui',
  'v-charts',
  'vuex',
  'vue-i18n',
  'html2canvas',
  'vue-amap',
  'vuedraggable',
  'vue-print-nb',
  'gcoord'
]

module.exports = {
  entry:{
    vendor: vendors
  },

  output:{
    path: path.resolve(__dirname, '../static/vendor'),      // 路径
    filename: '[name].[hash].js',   // 文件名
    library: '[name]'        //  与打包后的文件的返回值绑定
  },

  plugins:[
    new webpack.DllPlugin({
      context: __dirname,       // 可选参数，manifest 文件中请求的上下文(默认值为 webpack 的上下文)
      path: path.join(__dirname, '../static/vendor', '[name]-manifest.json'),   // manifest.json 文件的路径
      name: '[name]'     // 暴露出来的Dll函数名，必须跟output.library保持一致
    }),
  ]
}