module.exports = function tablePageFn(name = '') {
  return `
<!-- file-auto-create -->
<template>
  <div class="dst-app-container-table">
    <Filtrate :data="filtrate_data"
              @seachClick="searchClick" />
    <div class="dst-table-content">
      <div class="dst-table-over-btn-box">
        <el-button type="success">创建操作</el-button>
        <el-button @click="exportList" plain type="success">导出</el-button>
      </div>
      <DstTable :is-loading="configTable.isLock"
                :page-num="configTable.pageNum"
                :page-size="configTable.pageSize"
                :total="configTable.total"
                :table-head="headArray"
                :table-list="tableList"
                :config="configTable"
                @pageChange="pageChange"
                @sizeChange="sizeChange" />
    </div>
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import data from './data';
// import { exportFileAjax } from '@/portal-common/utils/exportFile';
export default {
  name: '',
  components: {},
  data() {
    return data.bind(this)();
  },

  computed: {},

  mounted() {
    this.getTableData();
  },

  methods: {
    getTableData() {
      //  let params = {
      //   pageSize:this.configTable.pageSize,
      //   pageNum:this.configTable.pageNum
      //  }
      this.configTable.isLock = true;
      // api().then(res=>{
      this.configTable.isLock = false;
      this.configTable.total = 0;
      // })
    },
    searchClick(e) {
      this.searchData = {
        ...this.searchData,
        ...e
      }
      this.getTableData()
    },
    exportList() {
      // exportFileAjax()
    },
    pageChange(e) {
      this.configTable.pageNum = e;
      this.getTableData();
    },
    sizeChange(e) {
      this.configTable.pageSize = e;
      this.configTable.pageNum = 1;
      this.getTableData();
    },
    editFn(e) {},
    deleteFn(e) {}
  }
};
</script>
<style lang="scss" scoped>

</style>
`
}
