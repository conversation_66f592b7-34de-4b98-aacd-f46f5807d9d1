import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取首次服务订单列表数据
export function getEquityservicesOrderList(data) {
  return request({
    url: rentalSaleUrl + '/equityservices/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询首次服务订单基础信息
export function getEquityservicesOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/equityservices/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
