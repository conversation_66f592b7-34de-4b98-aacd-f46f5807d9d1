<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'happlyRentalMarginOrders',
  components: {
    NewHapplyRentalMarginOrders: () => import('./index.vue'),
    OldHapplyRentalMarginOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldHapplyRentalMarginOrders'
    }
  },
  created() {
    this.init('OldHapplyRentalMarginOrders', 'NewHapplyRentalMarginOrders')
  }
}
</script>

<style lang="scss" scoped></style>
