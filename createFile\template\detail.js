module.exports = function detailPageFn(name = '') {
  return `
  <template>
  <div class="dst-detail-card-wrap"
       v-loading="isLock">
    <el-tabs v-model="activeName2"
             class="dst-card-tabs dst-add-overflow-y-box">
      <el-tab-pane label="基本信息"
                   name="first">
        <div>
          <el-card>
            <div slot="header">
              <span class="dst-card-title">模块名称(一行两列布局和独占一行布局格式)</span>
            </div>
            <div class="dst-clearfix">
              <div class="dst-detail-card-item">
                <div class="dst-detail-card-item-label">
                  label默认是140px
                </div>
                <div class="dst-detail-card-item-content ">
                  单行文本超出自动省略号显示你好fs多福多寿减肥呢几十年福建省妇女节那附近你今年说法就是987654321
                </div>
              </div>

              <div class="dst-detail-card-item">
                <div class="dst-detail-card-item-label">
                  字体悬浮弹窗
                </div>
                <div class="dst-detail-card-item-content ">
                  <el-tooltip class="item"
                              effect="dark"
                              content="建议使用《el-tooltip》悬浮组件肥呢几十年福建省妇女节那附肥呢几十年福建省妇女节那附肥呢几十年福建省妇女节那附肥呢几十年福建省妇女节那附"
                              placement="top-start">
                    <span>建议使用《el-tooltip》悬浮组件肥呢几十年福建省妇女节那附肥呢几十年福建省妇女节那附肥呢几十年福建省妇女节那附肥呢几十年福建省妇女节那附</span>
                  </el-tooltip>

                </div>
              </div>
              <div class="dst-detail-card-item">
                <div class="dst-detail-card-item-label">
                  文件
                </div>
                <div class="dst-detail-card-item-content">
                  <span class="dst-green-underline"
                        @click="showFile">点击文件名下载的示例</span>
                </div>
              </div>
              <div class="dst-detail-card-item">
                <div class="dst-detail-card-item-label">
                 一行两列
                </div>
                <div class="dst-detail-card-item-content">
                 默认是一行两列的
                </div>
              </div>
              <div class="dst-detail-card-item dst-block">
                <div class="dst-detail-card-item-label">
                  字体特殊颜色
                </div>
                <div class="dst-detail-card-item-content dst-font-danger">
                  <span class="dst-font-danger">红色=dst-font-danger</span><span class="dst-font-success">绿色=dst-font-success</span><span class="dst-font-warning">黄色=dst-font-warning </span><span class="dst-font-info">蓝色=dst-font-info 其他色待定</span>
                </div>
              </div>
              <div class="dst-detail-card-item dst-block">
                <div class="dst-detail-card-item-label">
                  独占一行示例
                </div>
                <div class="dst-detail-card-item-content">
                  默认是一行两列的-----《需要独占一行加class：dst-block》---《需要一行3列加class:dst-three-block》
                </div>
              </div>
              <div class="dst-detail-card-item dst-block">
                <div class="dst-detail-card-item-label">
                  多行文本示例
                </div>
                <div class="dst-detail-border-textarea">
                  示例多行textarea滚动显示：红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定
                  红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定其他色待定 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定红色=dst-font-danger 绿色=dst-font-success 黄色=dst-font-warning 蓝色=dst-font-info 其他色待定
                </div>
              </div>
            </div>
          </el-card>
          <el-card>
            <div slot="header">
              <span class="dst-card-title">模块名称(一行3列布局格式)</span>
            </div>
            <div class="dst-clearfix">
              <div class="dst-detail-card-item dst-three-block">
                <div class="dst-detail-card-item-label">
                  一行3列示例
                </div>
                <div class="dst-detail-card-item-content">
                  3列加class=dst-three-block
                </div>
              </div>
              <div class="dst-detail-card-item dst-three-block">
                <div class="dst-detail-card-item-label">
                  一行3列示例
                </div>
                <div class="dst-detail-card-item-content">
                  3列加class=dst-three-block
                </div>
              </div>
              <div class="dst-detail-card-item dst-three-block">
                <div class="dst-detail-card-item-label">
                  一行3列示例
                </div>
                <div class="dst-detail-card-item-content">
                  3列加class=dst-three-block
                </div>
              </div>

            </div>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="接收对象"
                   name="second">
        <div>333</div>
      </el-tab-pane>
    </el-tabs>
    <div class="dst-add-bottom-btn-box">
      <el-button type="success"
                 @click="submitForm">保存</el-button>
    </div>
  </div>
</template>
<script>
/* eslint-disable jsx-quotes */
export default {
  name: '${name}',
  components: {},
  data() {
    return {
      activeName2: 'first',
      isLock: false,
      ruleForm: {}
    }
  },
  mounted() {

  },
  methods: {
    // 展示文件
    showFile() {
      window.open('https://dst2.oss-cn-shenzhen.dstzc.com/dst2.0/export/dev/%E7%BB%93%E7%AE%97%E4%BF%A1%E6%81%AF20191108163619.xlsx?Expires=1576034009&OSSAccessKeyId=LTAIAnXMOKTk6Mvd&Signature=rNyqFc0Kk7tTaypgTR5g7q1fXmY%3D')
    },
  }
}
</script>
<style lang="scss">

</style>
`
}
