import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取退车车务订单列表数据
export function getBackcarserviceOrderList(data) {
  return request({
    url: rentalUrl + '/backcarservice/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询退车车务订单基础信息
export function getBackcarserviceOrderBasic(data) {
  return request({
    url: rentalUrl + '/backcarservice/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
