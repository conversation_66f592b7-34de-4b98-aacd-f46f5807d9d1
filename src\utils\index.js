import { Message } from 'element-ui'
import { isFunction, isJson, isObject } from '@/utils/types'
import { hasOwn, capitalize } from '@/utils/util'

/**
 * @description:递归格式化多层数据结构
 * @param {array} data 需要进行递归格式化的原数据
 * @param {boolean} flag 是否需要递归操作
 * @param {object} self 自定义格式化的对象数据
 * @param {string} childKey 子级键
 * @return:
 */
export function tranTreeData(data, flag, self = {}, childKey = 'childs') {
  return data.map((item) => {
    const cell = _.cloneDeep(item)
    if (item.name) {
      cell.label = item.name
    }
    if (item.id) {
      cell.value = item.id
    }
    for (const key in self) {
      if (hasOwn(self, key)) {
        cell[key] = item[self[key]]
      }
    }
    if (item[childKey] && item[childKey].length > 0) {
      if (flag) {
        cell.children = tranTreeData(item[childKey], flag, self)
      } else {
        cell.children = item[childKey].map((item) => {
          let val = {
            ...item,
            value: item.id,
            label: item.name,
          }
          for (const key in self) {
            if (hasOwn(self, key)) {
              val[key] = item[self[key]]
            }
          }
          return val
        })
      }
    }
    return cell
  })
}

/**
 * @description: 获取当前元素的属性
 * @param {type}
 * @return:
 */
export function getTargetStyle() {
  if (window.document.currentStyle) {
    return (dom, attr) => dom.currentStyle[attr]
  } else {
    return (dom, attr) => getComputedStyle(dom, false)[attr]
  }
}

/**
 * @description:数组扁平化
 * @param {array} arr 数组数据
 * @return:
 */
export function flatten(arr, key = 'children') {
  return arr.reduce((result, item) => {
    return result.concat(
      item,
      Array.isArray(item[key]) ? flatten(item[key], key) : []
    )
  }, [])
}

/**
 * @description:数组扁平化
 * @param {array} arr 数组数据
 * @return:
 */
export function flattenArr(arr) {
  return arr.reduce((result, item) => {
    return result.concat(item, Array.isArray(item) ? flatten(item) : [])
  })
}

/**
 * @description: 获取第n层级数据（对象数组）
 * @param {array} origin 对象数组数据源
 * @param {number} level 当前数据层级
 * @param {number} limit 获取对应层级
 * @param {string} key 通过某个属性来验证层级
 * @return:
 */
export function getCurrentData({
  origin = [],
  level = 0,
  limit = 3,
  key = 'childs',
}) {
  level++
  if (level === limit) {
    return origin.map((element) => {
      delete element[key]
      return element
    })
  } else {
    let list = []
    const childs = origin.filter((item) => item[key] && item[key].length)
    childs.forEach((element) => {
      element[key].forEach((item) => {
        list.push(item)
      })
    })
    return getCurrentData({ origin: list, level, limit, key })
  }
}

/**
 * @description:获取数据类型
 * @param {any} value 需要验证类型的数据
 * @return:
 */
export function getRawType(value) {
  return Object.prototype.toString.call(value).slice(8, -1)
}

/**
 * @description:对象间的属性兼容拷贝
 * @param {object} target 目标对象
 * @param {object} origin 来源对象
 * @param {object} params 需要拷贝的对象键值对
 * @param {boolean} deep 是否进行深层拷贝
 * @param {boolean} strict 是否需要严格一一对应
 * @return:
 */
export function cloneLimit(
  target,
  origin,
  params,
  deep = true,
  strict = false
) {
  const targetData = _.cloneDeep(target)
  for (const key in params) {
    const flag = strict
      ? Object.hasOwnProperty.call(params, key) &&
        Object.hasOwnProperty.call(origin, key)
      : Object.hasOwnProperty.call(params, key)
    if (flag) {
      if (deep) {
        targetData[key] = origin[key]
      } else {
        target[key] = origin[key]
      }
    }
  }
  return deep ? targetData : target
}

/**
 * @description:获取当前文件下的所有文件
 * @param {object} files 当前引入目录的信息
 * @param {boolean} lazy 是否懒加载
 * @return:
 */
export function importAll(files, lazy) {
  let __modules = {}
  if (!lazy) {
    files.keys().forEach((key) => {
      let m = files(key).default
      let n = m.name
      __modules[n] = m
    })
    return __modules
  } else {
    files.keys().forEach((item) => {
      let name = item.split('/')[1]
      if (name.includes('.')) {
        name = name.split('.')[0]
      }
      __modules[capitalize(name)] = () => files(item) // 直接提供一个返回 Promise 的函数
    })
    return __modules
  }
}
/**
 * @description:数组转化为对象
 * @param {object} target 转化后的对象
 * @param {array} origin 转化前的数组
 * @return:
 */
export function arrayToObj(target, origin) {
  const temp = Object.assign({}, target)
  origin.forEach((element) => {
    temp[element] = element
  })
  return temp
}

/**
 * @description:遍历树状数据的最后一层
 * @param {array} origin 数据源
 * @param {array} target 新数据
 * @param {string} key 遍历索引
 * @return:
 */
export function flattenTree(origin = [], target = [], key = 'childs') {
  origin.forEach((item) => {
    if (item[key] && item[key].length) {
      flattenTree(item[key], target, key)
    } else {
      target.push(item)
    }
  })
  return target
}

/**
 * @description: 合并部分属性
 * @param {Object} target 需要添加属性的对象
 * @return:
 */
export function merge(target) {
  for (let i = 1, j = arguments.length; i < j; i++) {
    let source = arguments[i] || {}
    for (let prop in source) {
      if (source.hasOwnProperty(prop)) {
        let value = source[prop]
        if (value !== undefined) {
          target[prop] = value
        }
      }
    }
  }
  return target
}

/**
 * @description:each每个元素，删除空childs
 * @param {object} node 数据源
 * @return:
 */
export function delEmptyChild(node) {
  node.forEach((item) => {
    if (item.childs && item.childs.length === 0) {
      delete item.childs
    }
    if (item.childs && item.childs.length > 0) {
      delEmptyChild(item.childs)
    }
  })
  return node
}

/**
 * @description:生成唯一标识
 * @param {type}
 * @return:
 */
export function uuid() {
  let d = new Date().getTime()
  if (window.performance && isFunction(window.performance.now)) {
    d += performance.now()
  }
  let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    function (c) {
      let r = (d + Math.random() * 16) % 16 | 0 // d是随机种子
      d = Math.floor(d / 16)
      return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
    }
  )
  return uuid
}

/**
 * @description:将数字转换成金额显示
 * @param {num} 金额数据
 * @return:
 */
export function toMoney(num) {
  if (!isEmpty(num)) {
    if (isNaN(num)) {
      return Message.warning('金额中含有不能识别的字符')
    }
    // 判断是否是字符串如果是字符串转成数字
    num = typeof num == 'string' ? parseFloat(num) : num
    num = num.toFixed(2) // 保留两位
    num = parseFloat(num) // 转成数字
    num = num.toLocaleString() // 转成金额显示模式
    // 判断是否有小数
    if (num.indexOf('.') == -1) {
      num = num + '.00'
    } else {
      num = num.split('.')[1].length < 2 ? num + '0' : num
    }
    // 返回的是具有千分位格式并保留2位小数的字符串
    return num
  } else {
    return num
  }
}

/**
 * @description:去除金额格式化
 * @param {type}
 * @return:
 */
export function reMoney(num) {
  // eslint-disable-next-line no-useless-escape
  return parseFloat(num.replace(/[^\d\.-]/g, ''))
}

/**
 * @description:百分比转化小数点
 * @param {string} str 百分比数据
 * @return:
 */
export function percentToPoint(str = '0%') {
  let data = str.replace('%', '')
  data = Number(data)
  if (isNaN(data)) {
    return this.$message.warning('当前数据不符合要求！')
  }
  const num = data / 100
  return num
}

/**
 * @description:小数点转化为百分比
 * @param {number | string} str 小数点数据
 * @param {boolean} flag 是否需要百分号
 * @return:
 */
export function pointToPercent(str = 0, flag) {
  const data = Number(str)
  if (isNaN(data)) {
    return this.$message.warning('当前数据不符合要求！')
  }
  const num = (data * 100).toFixed(2)
  const result = num + (flag ? '' : '%')
  return result
}

/**
 * @description:将价格转化为大写格式
 * @param {number} n 价格
 * @return:
 */
export function toChineseMoney(n) {
  if (!/^(0|[1-9]\d*)(\.\d+)?$/.test(n)) {
    console.error('数据非法！')
    return this.$message.warning('金额格式错误！')
  }
  let unit = '千百拾亿千百拾万千百拾元角分',
    str = ''
  n += '00'
  const p = n.indexOf('.')
  if (p >= 0) {
    n = n.substring(0, p) + n.substr(p + 1, 2)
  }
  unit = unit.substr(unit.length - n.length)
  for (let i = 0; i < n.length; i++) {
    str += '零壹贰叁肆伍陆柒捌玖'.charAt(n.charAt(i)) + unit.charAt(i)
  }
  return str
    .replace(/零(千|百|拾|角)/g, '零')
    .replace(/(零)+/g, '零')
    .replace(/零(万|亿|元)/g, '$1')
    .replace(/(亿)万/g, '$1')
    .replace(/^元零?|零分/g, '')
    .replace(/元$/g, '元整')
}

/**
 * @description:判断非空数据集
 * @param {string} str 判断非空数据
 * @return:
 */
export function isEmpty(str) {
  return (
    _.isNil(str) ||
    str === '' ||
    (Array.isArray(str) && !str.length) ||
    (isJson(str) && JSON.stringify(str) === '{}') ||
    (isObject(str) && !Object.keys(str).length)
  )
}

/**
 * @description:数组对象去重
 * @param {array} list 数组数据源
 * @param {string} prop 去重属性
 * @param {boolean} mapFlag 是否只需要返回当前属性
 * @return:
 */
export function unique(list, prop, mapFlag) {
  const res = new Map()
  let result = list.filter(
    (item) => !res.has(item[prop]) && res.set(item[prop], 1)
  )
  return mapFlag ? result.map((item) => item[prop]) : result
}

/**
 * @description:根据属性值获取属性名
 * @param {object} obj 对象数据源
 * @param {any} value 查找的属性值
 * @param {function} compare 查找方法
 * @return:
 */
export function findKey(obj, value, compare = (a, b) => a === b) {
  return Object.keys(obj).find((k) => compare(obj[k], value))
}

/**
 * @description:递归获取特定的数据值
 * @param {array} data 数据列表
 * @param {object} props 数据属性映射
 * @param {boolean} isStrictSquals 是否要严格全等模式
 * @param {boolean | string} value 查询匹配的数据值
 * @return:
 */
export function getChain({
  data = [],
  props = { id: 'id', children: 'children', name: 'name' },
  isStrictSquals = false, // 是否要严格全等模式
  value,
  callback = () => true,
}) {
  const dataList = []
  const valueList = []
  function searching(id, data) {
    for (let i in data) {
      let item = data[i]
      const child = Array.isArray(item[props.children])
        ? item[props.children]
        : []
      valueList.push(item[props.id])
      dataList.push(item)
      if (
        ((isStrictSquals && id === item[props.id]) ||
          (!isStrictSquals && id == item[props.id])) &&
        callback(item)
      ) {
        return true
      } else if (child.length && searching(id, child)) {
        return true
      }
      valueList.pop()
      dataList.pop()
    }
  }
  Array.isArray(data) && !isEmpty(data) && searching(value, data)
  return {
    items: dataList,
    values: valueList,
    data: !isEmpty(dataList) ? dataList[dataList.length - 1] : null,
    value: !isEmpty(valueList) ? valueList[valueList.length - 1] : null,
    label: !isEmpty(dataList)
      ? dataList[dataList.length - 1][props.name]
      : null,
  }
}

/**
 * @description:获取批量数据值
 * @param {type}
 * @return:
 */
export function getBatchCurrentValue(data) {
  if (!isEmpty(data)) {
    if (Array.isArray(data)) {
      return (data || []).map((item) => getChain(item))
    } else if (!isEmpty(data.value) && Array.isArray(data.value)) {
      return (data.value || []).map((item) =>
        getChain({
          ...data,
          value: item,
        })
      )
    } else return []
  } else return []
}

/**
 * @description:遍历获取最终的数据值
 * @param {array} val 遍历的数组数据
 * @param {array} origin 遍历的原数据
 * @param {number} n 遍历的数组层次
 * @param {object} props 遍历的属性映射
 * @param {array} list 层级数据存储
 * @return:
 */
export function recursionValue({
  val,
  origin,
  n = 0,
  props = {
    children: 'childs',
    value: 'id',
  },
  data = [],
  result = null,
}) {
  if (val && val.length) {
    const currentData = origin.find(
      (item) => item[props.value || 'id'] === val[n]
    )
    if (currentData) {
      data.push(currentData)
      const list = currentData[props.children || 'childs']
      if (val.length - 1 > n && list && list.length) {
        n++
        const item = recursionValue({ val, origin: list, n, props, data })
        result = item ? item.value : null
      } else {
        result = currentData
      }
    } else {
      result = null
    }
  } else {
    result = null
  }
  return {
    value: result,
    list: data,
  }
}

/**
 * @description:数字补零
 * @param {number} num 数字
 * @return:
 */
export function addZero(num) {
  if (typeof num === 'number') {
    if (num >= 0 && num < 10) {
      return '0' + num
    } else {
      return num.toString()
    }
  } else {
    return null
  }
}

/**
 * @description:遍历获取数组中的某项数据
 * @param {type}
 * @return:
 */
export function getCurrentKeyItem(
  list,
  target = [],
  keyObj,
  props = {
    name: 'name',
    id: 'id',
    code: 'code',
  }
) {
  list.forEach((item) => {
    if (!isEmpty(item.childs)) {
      getCurrentKeyItem(item.childs, target, keyObj, props)
    } else {
      const keyCode = Object.keys(keyObj)[0]
      if (keyCode && item[keyCode] === keyObj[keyCode]) {
        let obj = {}
        for (const key in props) {
          if (Object.hasOwnProperty.call(props, key)) {
            obj[key] = item[props[key]]
          }
        }
        target.push(obj)
      }
    }
  })
  return target
}

/**
 * @description:复杂对象合并
 * @param {object} origin 数据源
 * @param {object} target 合并数据
 * @param {object} result 合并结果
 * @param {string} prop 唯一标识
 * @param {function} callback 合并条件
 * @return:
 */
export function compositeMerge({
  origin,
  target,
  result = null,
  prop,
  callback,
}) {
  const fn = isFunction(callback) ? callback : () => true
  if (Array.isArray(origin)) {
    const initItem = result || []
    const targetValue = target || []
    const data = [...origin, ...targetValue, ...initItem]
    data.forEach((element) => {
      if (element && typeof element === 'object') {
        if (prop) {
          const index = initItem.findIndex(
            (item) => item[prop] === element[prop]
          )
          if (index > -1) {
            fn(origin, target) &&
              initItem.splice(
                index,
                1,
                compositeMerge({ origin: element, prop })
              )
          } else {
            fn(origin, target) &&
              initItem.push(compositeMerge({ origin: element, prop }))
          }
        } else {
          fn(origin, target) &&
            initItem.push(compositeMerge({ origin: element, prop }))
        }
      } else if (element && !initItem.includes(element)) {
        fn(origin, target) && initItem.push(element)
      }
    })
    result = initItem
  } else if (fn(origin, target)) {
    const initItem = result || {}
    const targetValue = target || {}
    const data = { ...origin, ...targetValue, ...initItem }
    for (const key in data) {
      if (Object.hasOwnProperty.call(data, key)) {
        if (data[key] && typeof data[key] === 'object') {
          initItem[key] = compositeMerge({
            origin: origin[key],
            target: targetValue[key],
            result: initItem[key],
            prop,
          })
        } else {
          const item = data[key]
          if (item) {
            initItem[key] = item
          } else {
            initItem[key] = targetValue[key] || origin[key]
          }
        }
      }
    }
    result = initItem
  }
  return result
}

/**
 * @description:对象属性映射
 * @param {object} data 数据源
 * @param {object} itemMap 映射字段
 * @param {boolean} isDel 是否去除原有属性
 * @return:
 */
export function itemValueMap(data, itemMap, isDel) {
  const itemValue = _.cloneDeep(data)
  for (const key in itemValue) {
    if (Object.hasOwnProperty.call(itemValue, key)) {
      const index = Object.keys(itemMap).findIndex((item) => item === key)
      if (index > -1) {
        const currentKey = Object.values(itemMap)[index]
        itemValue[currentKey] = itemValue[key]
        isDel && delete itemValue[key]
      }
    }
  }
  return itemValue
}

/**
 * @description:格式化数组数据
 * @param {string | array} data 需要格式化数据集合
 * @param {string} key 需要格式化的字符串
 * @return:
 */
export function toggleSplitJoin(data, key) {
  if (isEmpty(data)) {
    return null
  } else {
    if (Array.isArray(data[key])) {
      return isEmpty(data[key]) ? '' : data[key].join(',') || ''
    } else if (typeof data[key] === 'string') {
      return isEmpty(data[key]) ? [] : data[key].split(',') || []
    } else {
      return null
    }
  }
}
