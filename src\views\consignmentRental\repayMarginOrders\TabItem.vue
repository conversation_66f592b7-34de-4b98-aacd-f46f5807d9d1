<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalRepayMarginOrders',
  components: {
    NewConsignmentRentalRepayMarginOrders: () => import('./index.vue'),
    OldConsignmentRentalRepayMarginOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalRepayMarginOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalRepayMarginOrders',
      'NewConsignmentRentalRepayMarginOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
