import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 分页查询替换车减免规则列表
export function getReplaceCarReduceConfigList(data) {
  return request({
    url: rentalUrl + '/config/order/replaceCarReduceConfig/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 替换车减免规则编辑回填
export function getReplaceCarReduceConfigFindById(data) {
  return request({
    url: rentalUrl + '/config/order/replaceCarReduceConfig/findById',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 编辑替换车减免规则
export function updateReplaceCarReduceConfig(data) {
  return request({
    url: rentalUrl + '/config/order/replaceCarReduceConfig/update',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 新增替换车减免规则
export function createReplaceCarReduceConfig(data) {
  return request({
    url: rentalUrl + '/config/order/replaceCarReduceConfig/create',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 禁用/启用替换车减免规则
export function updateReplaceCarReduceStatus(data) {
  return request({
    url: rentalUrl + '/config/order/replaceCarReduceConfig/updateStatus',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
