<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'carRentalEntitleServiceOrders',
  components: {
    NewCarRentalEntitleServiceOrders: () => import('./index.vue'),
    OldCarRentalEntitleServiceOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件，初始为空避免重复渲染
      activeName: ''
    }
  },
  created() {
    this.init('OldCarRentalEntitleServiceOrders', 'NewCarRentalEntitleServiceOrders')
  }
}
</script>

<style lang="scss" scoped></style>
