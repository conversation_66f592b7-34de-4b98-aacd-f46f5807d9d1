// var FileCreate = require('./create');
var create = require('./create');

// FileCreateFileSrc 2019-12-24 例子  参数设置 all detail add table 根目录views下，可自己设置
// npm run create /iot-charge/autoCreateFile all
// npm run create /iot-charge/autoCreateFile detail
// npm run create /iot-charge/autoCreateFile detail table add

// console.log(process.argv[2])
const argv = process.argv
console.log(argv)
if (argv.length <= 2) {
  new create.FileCreate(
    './src/views',
    // 创建文件地址，
    // networkManagement 设备入网管理
    // carMonitoring 车辆监控
    // chargingMonitoring 充电监测
    // chargeOperation 设备运维管理
    // FileManagement 设备档案管理
    // templateManager 设备参数模板管理
    // platform 平台对接管理
    // dataCenter 数据中心
    // sysManagement 系统管理
    [
      '/ccc t',
    ]
  )
} else {
  const fileParams = argv.filter((val, index) => {
    return index >= 2
  })

  new create.FileCreateFileSrc(
    fileParams, './src/views'
  )
  // new create.FileCreateOneFile(
  //   fileParams
  // )
}
