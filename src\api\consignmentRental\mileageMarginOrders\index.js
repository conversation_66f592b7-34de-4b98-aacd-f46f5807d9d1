import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取里程保证金订单列表数据
export function getLegendDepositOrderList(data) {
  return request({
    url: rentalSaleUrl + '/legendDeposit/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取里程保证金订单基础信息数据（根据id）
export function getLegendDepositOrderId(data) {
  return request({
    url: rentalSaleUrl + '/legendDeposit/order/findByOrderId',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取里程保证金订单基础信息数据（根据code）
export function getLegendDepositOrderCode(data) {
  return request({
    url: rentalSaleUrl + '/legendDeposit/order/findByOrderCode',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
