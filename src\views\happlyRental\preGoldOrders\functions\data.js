import { getSimpleDictByType } from '@/api/base'
import BatchInput from '@/views/common/common/batchSearch'
import dictionaries from '@/utils/common.js'
const { bookiStatus, relationOrderType } = dictionaries

/**
 * @description:退车车损订单查询配置项
 * @param {type}
 * @return:
 */
export function setFiltrateData() {
  return [
    {
      type: 'input',
      prop: 'bookingCode',
      label: '订单编码',
      attrs: {
        placeholder: '请输入订单编码',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'customerKey',
      label: '客户名称',
      width: '320px',
      attrs: {
        placeholder: '请输入客户名称',
        clearable: true,
        remote: true,
        filterable: true,
        reserveKeyword: true,
        remoteMethod: this.remoteCustomerMethod,
        loading: this.customerLoading,
      },
      options: this.customerList,
      on: { change: (val, data) => this.handleChangeCustomer(data, false) },
      ...this.filterSearchStyle,
    },
    {
      type: 'input',
      prop: 'orderCode',
      label: '关联欢乐租订单',
      attrs: {
        placeholder: '请输入关联欢乐租订单',
        clearable: true,
      },
      width: '400px',
      ...this.filterSearchStyle,
      labelWidth: '150px',
    },
    {
      type: BatchInput,
      prop: 'carNoList',
      label: '车牌号',
      attrs: {
        placeholder: '请输入车牌号',
        clearable: true,
      },
      width: '320px',
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'saleUserId',
      label: '业务归属',
      width: '320px',
      attrs: {
        placeholder: '请输入业务归属',
        clearable: true,
        remote: true,
        filterable: true,
        reserveKeyword: true,
        remoteMethod: this.remoteSellerMethod,
        loading: this.sellerLoading,
      },
      options: this.sellerList,
      props: { value: 'dst_user_id', label: 'dd_user_name_id' },
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'bookingStatus',
      label: '订单状态',
      optionsApiConf: {
        parmas: { type: bookiStatus },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'select',
      prop: 'orderType',
      label: '关联订单类型',
      optionsApiConf: {
        parmas: { type: relationOrderType },
        resDataPath: 'data',
        apiFun: getSimpleDictByType,
      },
      ...this.filterSearchStyle,
    },
    {
      type: 'DatePicker',
      prop: 'createTime',
      label: '创建时间',
      width: '400px',
      attrs: {
        clearable: true,
        rangeSeparator: '-',
        type: 'daterange',
        startPlaceholder: '请选择开始日期',
        endPlaceholder: '请选择结束日期',
      },
      valueType: [],
      labelWidth: '120px',
    },
  ]
}
