import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取安全保证金订单列表数据
export function getSafeDepositOrderList(data) {
  return request({
    url: rentalUrl + '/vehicleDeposit/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 根据code获取安全保证金订单列表数据
export function getSafeDepositOrderBasic(data) {
  return request({
    url: rentalUrl + '/vehicleDeposit/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
