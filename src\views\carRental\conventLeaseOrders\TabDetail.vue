<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  components: {
    NewCarRentalConventLeaseOrderDetail: () => import('./detail.vue'),
    OldCarRentalConventLeaseOrderDetail: () => import('./oldModel/detail.vue')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: ''
    }
  },
  created() {
    this.init('OldCarRentalConventLeaseOrderDetail', 'NewCarRentalConventLeaseOrderDetail')
  }
}
</script>

<style lang="scss" scoped></style>
