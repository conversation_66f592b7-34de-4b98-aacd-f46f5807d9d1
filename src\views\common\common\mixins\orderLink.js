/* eslint-disable indent */
import { linkRouter } from '@/utils/business'
import { isEmpty } from '@/utils'

export default {
  methods: {
    /**
     * @description:获取当前订单跳转对象所需要的地址参数
     * @param {number | string} orderType 订单类型
     * @param {object} data 当前订单数据信息
     * @param {string} primaryKey 当前订单编码具体标识键
     * @param {boolean} isNew 当前是否属于治理新模式
     */
    getOrderParams({ orderType, data, primaryKey, isNew }) {
      let query = {}
      if (orderType === 0) {
        const defaultKey = isNew ? 'orderCode' : 'parentOrderCode'
        query = {
          id: data.parentOrderId,
          code: data[primaryKey ? primaryKey : defaultKey],
          name: 'BaseInfo',
        }
      } else {
        const defaultKey = isNew ? 'subOrderCode' : 'orderCode'
        query = {
          id: data.id,
          code: data[primaryKey ? primaryKey : defaultKey],
        }
      }
      return query
    },
    /**
     * @description:跳转各种订单详情
     * @param {object} row 当前详情跳转需要的数据集合
     * value：当前具体值标识 (比如唯一值id,code等)
     * data：当前数据项（比如列表数据项，详情数据信息等）
     * type：当前系统内模块标识（当前处于系统中的什么模块）
     * orderType：当前系统内模块具体标识（当前处于系统模块中的什么类型）
     * primaryKey：当前订单编码具体标识键
     * isNew：当前是否属于治理新模式
     * params：当前跳转携带自定义参数
     */
    goDetail({ value, data, type, orderType, primaryKey, isNew, params }) {
      if (value && ['number', 'string'].includes(typeof orderType)) {
        let query = {}
        const result = this.getOrderParams({ orderType, data, primaryKey, isNew })
        query = { ...result, ...params }
        const defaultParentKey = isNew ? 'orderCode' : 'parentOrderCode'
        const defaultChildsKey = isNew ? 'subOrderCode' : 'orderCode'
        const defaultKey = orderType === 0 ? defaultParentKey : defaultChildsKey
        const { url } = linkRouter({
          // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
          type: !isEmpty(type)
            ? type
            : Number(data[primaryKey ? primaryKey : defaultKey].slice(0, 2)),
          orderType: !isEmpty(orderType)
            ? orderType
            : Number(data[primaryKey ? primaryKey : defaultKey].slice(2, 4)),
        })
        url && this.$router.push({ path: url, query })
      } else {
        this.$message.warning('跳转失败，请检查对应的订单编码')
      }
    },
  },
}
