import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { tradeUrl } = common

// 分页查询交易模板列表
export function getTradeTemplateList(data) {
  return request({
    url: tradeUrl + '/trade/template/queryPage',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 交易模板编辑回填
export function getTradeTemplateItem(data) {
  return request({
    url: tradeUrl + '/trade/template/query',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 交易模板规则列表
export function getTradeRuleQuery() {
  return request({
    url: tradeUrl + '/trade/rule/list',
    method: 'get',
    baseURL: process.env.BASE_API12,
  })
}

// 编辑交易模板
export function updateTradeTemplate(data) {
  return request({
    url: tradeUrl + '/trade/template/update',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 新增交易模板
export function createTradeTemplate(data) {
  return request({
    url: tradeUrl + '/trade/template/add',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 禁用/启用交易模板
export function updateTradeTemplateStatus(data) {
  return request({
    url: tradeUrl + '/trade/template/isEnabled',
    method: 'post',
    data,
    baseURL: process.env.BASE_API12,
  })
}

// 检查模板是否重名
export function checkTemplateName(data) {
  return request({
    url: tradeUrl + '/trade/template/checkTemplateName',
    method: 'get',
    data,
    baseURL: process.env.BASE_API12,
  })
}
