<!-- table下按钮组 -->
<template>
  <span v-if="list.length != 0">
    <template v-if="list.length <= 3">
      <el-button
        v-for="(item, index) in list"
        :key="index"
        size="mini"
        @click="item.click"
        :type="item.type || 'success'"
        plain
        :disabled="item.disabled"
      >{{ item.value }}</el-button>
    </template>
    <template v-else>
      <el-button
        v-for="(item, index) in list"
        v-show="index < 2"
        :key="index"
        :type="item.type || 'success'"
        plain
        size="mini"
        @click="item.click"
      >{{ item.value }}</el-button>
      <el-dropdown size="mini">
        <el-button plain size="mini">
          更多
          <i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in list" v-show="index >= 2" :key="index">
            <el-button
              :type="item.type || 'success'"
              plain
              size="mini"
              @click="item.click"
              :disabled="item.disabled"
            >{{ item.value }}</el-button>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </template>
  </span>
</template>

<script>
/**
 * 已全局注册-----DstBtns 一般主要用于在DstTable组件中，当需要显示的按钮大于3个时，会仅展示前2个按钮，其他按钮自动收起在更多按钮中。点击更多可以展开查看其他被隐藏的按钮
 * @displayName DstBtns按钮组件
 */
export default {
  name: 'SysBtns',
  components: {},
  props: {
    /**
     * 按钮的配置项，例如： [{value: '详情', click: goDetailPage.bind(v, scope.row, 'detail'), isShow: true},]
     */
    btnList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      list: []
    }
  },
  computed: {},
  watch: {
    btnList: {
      handler: function(val, oldVal) {
        let listArr = []
        this.btnList.forEach(item => {
          if (item.isShow !== false) {
            listArr.push(item)
          }
        })
        this.list = listArr
      },
      immediate: true
    }
  },

  mounted() {},

  methods: {}
}
</script>
<style lang="scss" scoped>
::deep.el-dropdown-menu__item {
  margin: 5px 0px;
}
::deep.el-dropdown {
  height: 24px;
}
::deep.el-button--mini {
  height: 24px;
}
::deep.el-dropdown-menu__item:hover {
  background: #fff;
}

/deep/.el-dropdown-menu__item {
  .el-button.is-plain {
    // border: none;
    outline: 0;
    background: transparent;
    &:hover {
      color: #8d9eff;
    }
  }
  &:hover {
    .el-button.is-plain {
      color: #8d9eff;
      border-color: #8d9eff;
    }
  }
}
</style>
