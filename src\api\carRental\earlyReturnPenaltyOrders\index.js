import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取提前退车违约金订单列表数据
export function getBackcarliquidateddamagesOrderList(data) {
  return request({
    url: rentalUrl + '/backcarliquidateddamages/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 查询提前退车违约金订单基础信息
export function getBackcarliquidateddamagesOrderBasic(data) {
  return request({
    url: rentalUrl + '/backcarliquidateddamages/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
