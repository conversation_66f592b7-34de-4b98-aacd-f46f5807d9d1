```vue
<template>
<el-card>
  <div slot="header">
    <span class="dst-card-title">表格--常见使用-推荐</span>
  </div>
  <div>
    <div class="oss-padding10">当按钮超出3个时隐藏部分按钮：</div>
    <DstBtns :btn-list="btnList"/>
    <div class="oss-padding10">当按钮不超出3个时：</div>
    <DstBtns :btn-list="btnList2"/>
  </div>
</el-card>
</template>
<script>

import DstBtns from '@/portal-common/components/DstBtns/index.vue'
export default {
  name: 'xxxxx33',
  components: { DstBtns },
  data() {
    return {
      btnList: [
          {
            value: '详情',
            click: this.add,
            isShow: true
          },
          {
            value: '审批',
            click: this.add,
            isShow: true
          },
           {
            value: '审批2',
            click: this.add,
            isShow: true
          },
           {
            value: '审批3',
            click: this.add,
            isShow: true
          }, {
            value: '审批4',
            click: this.add,
            isShow: true
          },
      ],
      btnList2: [
           {
            value: '编辑',
            click: this.add,
            isShow: true
          }, {
            value: '审批4',
            click: this.add,
            isShow: true
          },
      ],
    }
  },
  methods: {
    add() {
      alert('你点击了按钮')
    },
  }
}
</script>
```