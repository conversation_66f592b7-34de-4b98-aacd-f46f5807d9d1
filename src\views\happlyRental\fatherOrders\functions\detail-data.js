/* eslint-disable jsx-quotes */
import { showTime } from '@/utils/system/business'

/**
 * @description:基础信息1
 */
export function baseInfo1() {
  return [
    { prop: 'orderCode', label: '订单编号', type: 'text', class: 'font-bold' },
    { prop: 'createTime', label: '创建时间', type: 'text' },
    { prop: 'payEndTime', label: '支付截止时间', type: 'text' },
    { prop: 'carUseName', label: '车辆用途', type: 'text' },
    { prop: 'isBondName', label: '保证金', type: 'text', class: 'font-bold' },
    { prop: 'performTypeName', label: '履约条件', type: 'text' },
    { prop: 'cancelTime', label: '取消时间', type: 'text' },
  ]
}

/**
 * @description:基本信息2
 */
export function baseInfo2(v) {
  return [
    { prop: 'orderSourceName', label: '下单渠道', type: 'text' },
    { prop: 'orderChannel', label: '订单渠道', type: 'text' },
    { prop: 'cityName', label: '城市', type: 'text' },
    { prop: 'subName', label: '集团归属', type: 'text' },
    { prop: 'regionName', label: '大区归属', type: 'text' },
    { prop: 'serviceOrgCityName', label: '服务组织', type: 'text' },
    { prop: 'marketingOrgName', label: '营销组织', type: 'text' },
    { prop: 'customerGroupTypeName', label: '业务通路', type: 'text' },
    { prop: 'carNo', label: '车牌号', type: 'text', class: 'font-bold' },
    { prop: 'vinCode', label: '车架号', type: 'text', class: 'font-bold' },
    { prop: 'expDeliveryTime', label: '预计交车时间', type: 'text' },
    {
      prop: 'expireTime',
      label: '订单有效期',
      type: 'render',
      class: 'font-bold',
      render: (h, ctx) => {
        const text = showTime([
          v.baseInfoForm.expireStartTime,
          v.baseInfoForm.expireEndTime,
        ])
        return text !== '-' ? (
          <el-tooltip effect="dark" content={text} placement="top">
            <div class="font-bold font-text">{text}</div>
          </el-tooltip>
        ) : (
          <span> - </span>
        )
      },
    },
    { prop: 'lossScenePaymentName', label: '定损支付方', type: 'text' },
    {
      prop: 'cancelReason',
      label: '取消原因',
      type: 'text',
      class: 'dst-block',
    },
    {
      label: '创单审批截图',
      prop: 'ddPicture',
      type: 'dstUpload',
      listType: 'picture-card',
      isTooltip: true,
      filterOpts: { type: 'jpg,png', size: 40 },
      props: {
        fileName: 'fileName', // 文件名称
        fileUrl: 'accessUrl', // 用来展示的url
        saveUrl: 'fileUrl', // 就是后端需要保存的路径key值
      },
      modeType: 'detail',
    },
    {
      label: '退款审批截图',
      prop: 'refundApprovalPicture',
      type: 'dstUpload',
      listType: 'picture-card',
      isTooltip: true,
      filterOpts: { type: 'jpg,png', size: 40 },
      props: {
        fileName: 'fileName', // 文件名称
        fileUrl: 'accessUrl', // 用来展示的url
        saveUrl: 'fileUrl', // 就是后端需要保存的路径key值
      },
      modeType: 'detail',
    },
    {
      prop: 'orderRecommenderInfo',
      label: '推荐人',
      type: 'slot',
      class: 'dst-block order-recommender-info',
    },
    {
      prop: 'orderSalemansInfo',
      label: '业务归属',
      type: 'slot',
      class: 'dst-block order-saleman-info',
    },
  ]
}
