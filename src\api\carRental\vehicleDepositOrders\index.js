import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalUrl } = common

// 获取车辆保证金订单列表数据
export function getVehicleDepositOrderList(data) {
  return request({
    url: rentalUrl + '/vehicleDeposit/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 根据id获取车辆保证金订单列表数据
export function getVehicleDepositOrderId(data) {
  return request({
    url: rentalUrl + '/vehicleDeposit/order/id',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 根据code获取车辆保证金订单列表数据
export function getVehicleDepositOrderBasic(data) {
  return request({
    url: rentalUrl + '/vehicleDeposit/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
