import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取退车违约订单列表数据
export function getBackcarliquidateddamagesOrderList(data) {
  return request({
    url: rentalSaleUrl + '/backcarliquidateddamages/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取退车违约订单基础信息数据（根据code）
export function getBackcarliquidateddamagesOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/backcarliquidateddamages/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

