<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <el-tabs
      class="dst-card-tabs"
      :class="{ 'dst-card-new-tabs': !isShowBtn }"
      v-model="activeName"
      @tab-click="changeTab"
    >
      <el-tab-pane v-for="item in tabList" :key="item.name" :label="item.label" :name="item.name">
        <component
          v-if="activeName === item.name && baseInfoForm"
          :is-new="false"
          :base-info-form="baseInfoForm"
          :is="activeName"
          :ref="activeName"
          :parent-id="parentId"
          :parent-code="parentCode"
          :type="type"
          :customer-info-form="customerInfoForm"
          @parentRefresh="refresh"
        ></component>
      </el-tab-pane>
    </el-tabs>
    <div v-if="isShowBtn" class="dst-add-bottom-btn-box">
      <sys-button :btn-list="btnList" :limit="14" fold flex></sys-button>
    </div>
    <template v-if="componentKey">
      <component
        :is="componentKey"
        :ref="componentKey"
        :type="type"
        :parent-code="parentCode"
        :parent-id="parentId"
        :customer-info-form="customerInfoForm"
        :transfer-car-reason="transferCarReason"
        :car-depot-list="carDepotList"
        :store-mainte="storeMainte"
        :component-key.sync="componentKey"
        :abnormal-delivery-flag.sync="abnormalDeliveryFlag"
        @refresh="refresh"
      ></component>
    </template>
    <abnormal-delivery
      v-if="abnormalDeliveryFlag"
      ref="abnormalDelivery"
      :type="type"
      :city-id="cityId"
      :stock-table-visible="stockTableVisible"
      :visible.sync="abnormalDeliveryFlag"
      :show-stock-dialog.sync="showStockDialog"
      :parent-code="parentCode"
      :parent-id="parentId"
      :base-info-form="baseInfoForm"
      :customer-info-form="customerInfoForm"
      @refresh="refresh"
      @openStockTable="openStockTable"
    ></abnormal-delivery>
    <!-- 库存充足回显库存弹框 -->
    <stock-table-dialog
      v-if="stockTableVisible && showStockDialog"
      :order-number="1"
      goods-sales-name="车型族群:小面"
      goods-name="许老板的先租后买商品"
      :city-id="cityId"
      goods-sales-id="545642578788220928"
      @handleClose="stockTableClose"
    />
  </div>
</template>

<script>
/* eslint-disable jsx-quotes */
import btnMixin from './mixins/btnMixin'
import DialogComponents from './dialog'
import BaseInfo from './tabs/BaseInfo'
import commonDialog from '@/views/common/parentOrder/dialog/detail'
import commonTabs from '@/views/common/parentOrder/tabs'
import {
  getParentOrderBasic,
  getParentOrderCustomer
} from '@/api/common/rental/parent'
export default {
  name: 'OldCarRentalFatherOrderDetail',
  components: {
    AbnormalDelivery: () => import('./components/abnormalDelivery'),
    BaseInfo,
    ...DialogComponents,
    ...commonDialog,
    ...commonTabs,
    StockTableDialog: () => import('./dialog/StockTableDialog.vue')
  },
  mixins: [btnMixin],
  data() {
    return {
      // 加载对应的组件
      activeName: '',
      // 对应tabs的信息
      tabList: [
        {
          name: 'BaseInfo',
          label: '基础信息'
        },
        {
          name: 'EquityPrefer',
          label: '权益/优惠'
        },
        {
          name: 'AgreeInfo',
          label: '交还车信息'
        },
        {
          name: 'AgreeModule',
          label: '履约信息'
        },
        {
          name: 'ChildOrders',
          label: '子订单信息'
        },
        {
          name: 'OffsetBalance',
          label: '轧差订单'
        },
        {
          name: 'OrderAdjustment',
          label: '订单调整'
        },
        {
          name: 'OrderLog',
          label: '订单日志'
        }
      ],
      // 场站地点
      carDepotList: [],
      // 维保门店
      storeMainte: [],
      // 还车原因
      returnCarReason: [],
      // 换车原因
      transferCarReason: [],
      // 是否需要loading
      isLock: false,
      // 弹框组件key
      componentKey: '',
      // 车辆资源弹框开关显示
      vehicleResourcesFlag: false,
      // 异常交车抽屉开关显示
      abnormalDeliveryFlag: false,
      // 库存充足回显库存弹框开关
      stockTableVisible: false,
      // 当前查询库存的城市id
      cityId: '',
      // 是否打开库存弹框
      showStockDialog: false,
      // 获取父订单详情数据
      baseInfoForm: null,
      // 获取父订单客户数据
      customerInfoForm: {}
    }
  },
  computed: {
    // 获取当前父订单id
    parentId() {
      return this.$route.query.id
    },
    // 获取当前父订单code
    parentCode() {
      return this.$route.query.code
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type() {
      return (this.parentCode && Number(this.parentCode.slice(0, 2))) || 0
    },
    // 判断是否存在按钮显示
    isShowBtn() {
      return this.btnList.some(item => !!item.show)
    }
  },
  created() {
    if (this.$route.query.name) {
      this.activeName = this.$route.query.name
    } else {
      this.activeName = 'BaseInfo'
    }
    this.tabList = this.isShowPortalLog()
      ? this.tabList
      : this.tabList.filter(item => !item.label.includes('日志'))
    this.init()
  },
  methods: {
    /**
     * @description:切换tab栏触发
     * @param {type}
     * @return:
     */
    changeTab() {
      this.$router.push({
        path: '/carRental/fatherOrders/detail',
        query: {
          id: this.parentId,
          name: this.activeName,
          code: this.parentCode
        }
      })
    },
    /**
     * @description:刷新数据
     * @param {type}
     * @return:
     */
    refresh() {
      this.$nextTick(() => {
        this.init()
        this.$refs[this.activeName] &&
          typeof this.$refs[this.activeName][0].refresh === 'function' &&
          this.$refs[this.activeName][0].refresh()
      })
    },
    /**
     * @description:关闭库存列表弹框
     * @param {type}
     * @return:
     */
    stockTableClose() {
      this.stockTableVisible = false
      this.showStockDialog = false
    },
    /**
     * @description:查看是否存在库存
     * @param {boolean} flag 是否存在库存
     * @param {string} id 城市id
     * @return:
     */
    openStockTable(flag, id) {
      this.stockTableVisible = !!flag
      this.cityId = id
    },
    /**
     * @description:初始化基础信息数据
     * @param {type}
     * @return:
     */
    async init() {
      this.isLock = true
      await Promise.all([
        getParentOrderBasic({ parentOrderCode: this.parentCode }, this.type),
        getParentOrderCustomer({ parentOrderCode: this.parentCode }, this.type)
      ])
        .then(res => {
          const baseInfoForm = res[0].data || {}
          baseInfoForm.isPaidName = !this.$isEmpty(baseInfoForm.isPaid)
            ? baseInfoForm.isPaid == 1
              ? '是'
              : '否'
            : ''
          baseInfoForm.billingFulfillMethodName =
            baseInfoForm.billingFulfillMethodName || '场站履约计费'
          this.baseInfoForm = _.cloneDeep(baseInfoForm)
          this.customerInfoForm = res[1].data || {}
        })
        .finally(() => {
          this.isLock = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .dst-card-tabs {
  height: calc(100% - 60px) !important;
  &.dst-card-new-tabs {
    height: 100% !important;
  }
}
</style>
