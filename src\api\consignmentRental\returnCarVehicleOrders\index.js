import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, rentalSaleUrl } = common

// 获取退车车务订单列表数据
export function getBackcarserviceOrderList(data) {
  return request({
    url: rentalSaleUrl + '/backcarservice/order/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 获取退车车务订单基础信息数据（根据code）
export function getBackcarserviceOrderBasic(data) {
  return request({
    url: rentalSaleUrl + '/backcarservice/order/basic',
    method: 'get',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
