import { isObject } from '@/utils/types'
import { toggleSplitJoin } from '@/utils'

/**
 * @description:将批量查询字符串转换为数组格式（也可以反过来）
 * @param {array} filterConfig 列表查询配置
 * @param {object} params 列表查询参数
 * @param {string} prop 当前存在当前属性时，当前数据为批量查询模式
 * @return:
 */
export function toggleSplitJoinUtil(filterConfig, params, prop = 'batch') {
  const result = _.cloneDeep(params)
  const config = filterConfig.filter(
    (item) =>
      (isObject(item.type) && item.type.name === 'BatchSearch') ||
      item.modelType === 'batch' ||
      (typeof item[prop] === 'boolean' && item[prop])
  )
  config.forEach((item) => {
    result[item.prop] = toggleSplitJoin(result, item.prop)
  })
  return result
}

/**
 * @description:拆分查询项月份数据
 * @param {object} data 当前的数据集合
 * @param {key} string 所对应的字段名称
 */
export function getMouthData(data, key) {
  const dealData = (data) => {
    return data !== '0' ? (data ? Number(data) : data) : null
  }
  if (data[key]) {
    const arr = data[key].split('-')
    const pararms = {
      start: dealData(arr[0]),
      end: dealData(arr[1]),
    }
    delete data[key]
    return pararms
  } else {
    delete data[key]
    return { start: '', end: '' }
  }
}
