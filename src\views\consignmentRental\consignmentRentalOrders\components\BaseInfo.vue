<template>
  <sys-card title="基础信息" slot-name="baseInfoForm">
    <template slot="baseInfoForm_body">
      <dstForm
        ref="ruleForm"
        label-width="140px"
        mode-type="detail"
        v-model="baseInfoForm"
        :data-arr="baseInfo"
      ></dstForm>
    </template>
  </sys-card>
</template>

<script>
import { showTime } from '@/utils/system/business'
import orderMixins from '@/views/common/common/mixins/orderLink'
import contractMixins from '@/views/common/common/mixins/contractMixins'
export default {
  name: 'BaseInfo',
  mixins: [orderMixins, contractMixins],
  props: {
    // 基本信息数据
    baseInfoForm: {
      type: Object,
      default: () => ({}),
      required: true
    },
    // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
    type: {
      type: [String, Number],
      default: 1,
      required: true
    }
  },
  computed: {
    // 基础信息详情配置
    baseInfo() {
      return [
        {
          prop: 'orderCode',
          label: '订单编号',
          type: 'text',
          class: 'font-bold'
        },
        { prop: 'createTime', label: '创建时间', type: 'text' },
        { prop: 'cancelTime', label: '取消时间', type: 'text' },
        {
          prop: 'orderStatusName',
          label: '订单状态',
          type: 'text',
          class: 'font-bold'
        },
        {
          prop: 'orderTypeName',
          label: '订单类型',
          type: 'text',
          class: 'font-bold'
        },
        {
          prop: 'orderCode',
          label: '关联父订单',
          type: 'render',
          class: this.baseInfoForm.orderCode ? 'font-bold' : 'none-data',
          render: (h, item, parent, { form }) => {
            const orderCode = this.baseInfoForm.orderCode
            return orderCode ? (
              <el-tooltip effect='dark' content={orderCode} placement='top'>
                <sys-link
                  underline={false}
                  onClick={() =>
                    this.goDetail({
                      value: this.baseInfoForm.orderCode,
                      data: this.baseInfoForm,
                      orderType: 0
                    })
                  }
                >
                  <div class='dst-add-input font-bold'>{orderCode}</div>
                </sys-link>
              </el-tooltip>
            ) : (
              <span> - </span>
            )
          }
        },
        {
          prop: 'contractCode',
          label: '合同编码',
          type: 'render',
          class: this.baseInfoForm.contractCode ? 'font-bold' : 'none-data',
          render: (h, item, parent, { form }) => {
            const {
              contractCode,
              contractType,
              contractId,
              contractStyle
            } = this.baseInfoForm
            return contractCode ? (
              <el-tooltip effect='dark' content={contractCode} placement='top'>
                <sys-link
                  underline={false}
                  disabled={contractType == 9}
                  color={contractType == 9 ? '#00a680' : ''}
                  onClick={() =>
                    this.getContractStyle({
                      type: contractType,
                      id: contractId,
                      code: contractCode,
                      style: contractStyle
                    })
                  }
                >
                  <div class='dst-add-input font-bold'>{contractCode}</div>
                </sys-link>
              </el-tooltip>
            ) : (
              <span> - </span>
            )
          }
        },
        {
          prop: 'secondPartyName',
          label: '合同乙方',
          type: 'text',
          isTooltip: this.baseInfoForm.secondPartyName,
          class: this.baseInfoForm.secondPartyName ? 'font-bold' : 'none-data'
        },
        {
          prop: 'firstPartyName',
          label: '合同甲方',
          type: 'text',
          isTooltip: this.baseInfoForm.firstPartyName,
          class: this.baseInfoForm.firstPartyName ? 'font-bold' : 'none-data'
        },
        { prop: 'invoiceName', label: '开票主体', type: 'text' },
        { prop: 'operateName', label: '运营主体', type: 'text' },
        { prop: 'carNo', label: '车牌号', type: 'text', class: 'font-bold' },
        { prop: 'vinCode', label: '车架号', type: 'text', class: 'font-bold' },
        {
          prop: 'validityTime',
          label: '订单有效期',
          type: 'render',
          class: 'font-bold',
          render: (h, ctx) => {
            const text = showTime([
              this.baseInfoForm.validityBeginTime,
              this.baseInfoForm.validityEndTime
            ])
            return text !== '-' ? (
              <el-tooltip effect='dark' content={text} placement='top'>
                <div class='font-bold font-text'>{text}</div>
              </el-tooltip>
            ) : (
              <span> - </span>
            )
          }
        },
        { prop: 'payModeName', label: '首次支付范围', type: 'text' },
        { prop: 'cancelReason', label: '取消原因', type: 'text' }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/style/carRental/common';
</style>
