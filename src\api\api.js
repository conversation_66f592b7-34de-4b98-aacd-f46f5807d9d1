// api接口
import demand from './api/demand'
const apiUrl = {
  configData: {
    ddUserList: '/operateCore/dd/user/list',
    addArea: '/operateCore/area/addArea',
    // getServeOrgTree: '/operateCore/serveorg/getServeOrgTree'，
    getServeOrgTree: '/operateCore/serveorg/getServeOrgTree'
  },
  customerOperation: {
    exportFixedLoss: '/vehicleOperate/oss/backcar/exportFixedLoss', // 验车单列表导出
    carRentalList: '/vehicleOperate/oss/carRental/queryListPage', // 车辆租赁记录
    carRentalExport: '/vehicleOperate/oss/carRentalexport', // 车辆租赁导出

    contractTemplateList: '/marketing/oss/contractTemplate/list', // 协议合同模板列表
    contractTemplateCreateOrUpdate:
      '/marketing/oss/contractTemplate/createOrUpdate', // 合同模板保存或更新

    displaycarSaveOrder: '/marketing/oss/displaycar/saveOrder', // 新建展示车合同
    displaycarUpdateOrder: '/marketing/oss/displaycar/updateOrder', // 编辑展示车合同
    displaycarUploadOrderFile: '/marketing/oss/displaycar/uploadOrderFile', // 展示车合同上传附件、补充协议
    displaycarOverOrder: '/marketing/oss/displaycar/overOrder', // 展示车终止合同
    displaycarUpdateOrderStatusInvalid:
      '/marketing/oss/displaycar/updateOrderStatusInvalid', // 展示车合同作废

    getCarListByServiceCode: '/marketing/oss/order/getCarListByServiceCode', // 续租迁移合同车列表
    getCarInfoByClientCodeAndEndTime:
      'vehicleOperate/oss/pickcarOrder/getCarInfoByClientCodeAndEndTime', // 续租迁移显示车列表
    createPickcarOrderByRenewLease:
      'vehicleOperate/oss/pickcarOrder/createPickcarOrderByRenewLease', // 续租迁移操作

    orderList: '/marketing/oss/order/queryOrderList', // 合同列表&展示车合同列表(orderType:5)
    orderDetail: '/marketing/oss/order/queryOrderDetail', // 合同详情
    getVersionNumberList:
      '/marketing/oss/contractTemplate/getVersionNumberList', // 合同版本(展示车合同type=10)
    getProtocolListByAudited:
      '/marketing/oss/protocol/getProtocolListByAudited', // 渠道协议编码
    // getCustomerInfoListPage: '/marketing/customerinfo/getCustomerInfoListPage', //合同客户,2019年1月16日09:38:51，ywl应乐洲要求旧换新，只换接口不换请求方式和参数
    getCustomerInfoListPage:
      '/marketing/customerinfo/getSimpleCustomerInfoList', // 合同客户
    reviewOrder: '/marketing/oss/order/reviewOrder', // 确认合同
    overOrder: '/marketing/oss/order/overOrder', // 终止合同
    updateOrderStatusInvalid: '/marketing/oss/order/updateOrderStatusInvalid', // 作废合同
    queryOrderAttachFile: '/marketing/oss/order/queryOrderAttachFile', // 附件、补充协议列表查询接口
    updateOrderFile: '/marketing/oss/order/updateOrderFile', // 附件、补充协议批注
    delOrderAttachFile: '/marketing/oss/order/delOrderAttachFile', // 删除附件、补充协议
    exportCheck: '/marketing/oss/order/exportCheck', // 合同导出-检查
    exportExcel: '/marketing/oss/order/exportExcel', // 合同导出-导出
    updateOldServiceCode: '/marketing/oss/order/updateOldServiceCode', // 修改合同编码
    uploadOrderFile: '/marketing/oss/order/uploadOrderFile', // 上传附件、补充协议
    getProtocolInfoByCode: '/marketing/oss/protocol/getProtocolInfoByCode', // 根据协议编码获取协议信息
    saveOrder: '/marketing/oss/order/saveOrder', // 创建合同
    updateOrder: '/marketing/oss/order/updateOrder', // 修改合同
    updateOrderSaleman: '/marketing/oss/order/updateOrderSaleman', // 变更归属业务员
    updateOrderCarInfo: '/marketing/oss/order/updateOrderCarInfo', // 变更合同车辆类型
    updatePartbInfo: '/marketing/oss/order/updatePartbInfo', // 变更合同乙方

    // 3协议管理
    queryProtocolList: '/marketing/oss/protocol/queryProtocolList', // ywl协议管理列表页接口
    createProtocol: '/marketing/oss/protocol/createProtocol', // 创建协议
    getBrandTypeTree: '/operateBasic/brandType/getBrandTypeTree', // 品牌型号
    // getVersionNumberList: '/marketing/oss/contractTemplate/getVersionNumberList', // 协议版本
    getCustomerInfoById: '/marketing/oss/protocol/getCustomerInfo/', // 获取客户详情
    getProtocolInfoById: '/marketing/oss/protocol/getProtocolInfoById', // 详情和编辑拉取详情
    // getProtocolInfoByCode: '/marketing/oss/protocol/getProtocolInfoByCode', // 详情和编辑拉取详情
    queryProtocolAttachList: '/marketing/oss/protocol/queryProtocolAttachList', // 补充协议/附件列表
    delProtocolAttachFile: '/marketing/oss/protocol/delProtocolAttachFile', // 补充协议/附件列表
    queryContractOrderList: '/marketing/oss/order/queryOrderList', // 合同列表
    checkProtocolStatus: '/marketing/oss/protocol/checkProtocolStatus', // 修改协议状态，确认按钮
    postilProtocolAttachFile:
      '/marketing/oss/protocol/postilProtocolAttachFile', // 协议附件操作里面的批注按钮
    updateProtocolCode: '/marketing/oss/protocol/updateProtocolCode', // 修改编码
    uploadProtocolFile: '/marketing/oss/protocol/uploadProtocolFile', // 上传文件
    findRelationChannelList: '/marketing/oss/protocol/findRelationChannelList', // 渠道协议编码

    createNoContract: '/marketing/oss/nocontract/pickcar/createNoContract', // 创建无合同
    updateNoContract: 'marketing/oss/nocontract/pickcar/updateNoContract', // 修改无合同
    queryNoContract: 'marketing/oss/nocontract/pickcar/queryNoContract', // 无合同列表查询
    queryNoContractDetail:
      'marketing/oss/nocontract/pickcar/queryNoContractDetail', // 无合同详情
    updateNoContractStatus:
      'marketing/oss/nocontract/pickcar/updateNoContractStatus', // 修改无合同状态
    reviewOrderSure: 'marketing/oss/nocontract/pickcar/reviewOrder', // 审核
    getNoContractListByReviewed:
      'marketing/oss/nocontract/pickcar/getNoContractListByReviewed', // 获取已审核无合同单列表
    getSimpleCurrentOrgs: '/operateCore/org/getSimpleCurrentOrgs', // 用户组织树
    getSimpleDictByType: '/operateCore/dict/getSimpleDictByType',
    getSimpleCustomerInfoList:
      '/marketing/customerinfo/getSimpleCustomerInfoList', // 当前客户列表
    getOrgById: '/operateCore/org/getOrgById', // 选择乙方，带出电话和地址
    updateProtocolSaleman: '/marketing/oss/protocol/updateProtocolSaleman' // 修改归属业务员
  },
  common: {
    base2: process.env.BASE_API,
    uploadFile2: '/operateCore/api/oss/uploadFile',
    deptList: '/operateCore/dd/dept/list',
    // getSimpleDictByType: '/operateCore/dict/getSimpleDictByType',
    getSimpleDictByType: '/operateCore/dict/getSimpleDictByType',
    listPage: '/operateCore/dd/user/list',
    getSimpleCurrentOrgs: '/operateCore/org/getSimpleCurrentOrgs', // 获取组织列表
    getAreaLinkage: '/operateCore/area/getAreaLinkage', // 获取省市区下拉列表
    getSimpleBrandTypeList: '/operateBasic/brandType/getSimpleBrandTypeList',
    auth: '/dst-apis/sso/auth', // 权限
    loginfoList: '/log/loginfoList', // 操作日志
    base: process.env.BASE_API2
  },
  insuranceManagement: {
    // 保险管理模块
    insuranceList: '/insurance/dangerRecord/listPage', // 出险列表
    yesImportExcel: '/insurance/dangerRecord/yesImportExcel', // 出险数据已决批量导入
    notImportExcel: '/insurance/dangerRecord/notImportExcel', // 出险数据未决批量导入
    downNoExcel: '/insurance/dangerRecord/downNoExcel', // 出险数据未决模板下载
    downYesExcel: 'insurance/dangerRecord/downYesExcel' // 出险数据已决模板下载
  },
  productServer: {
    brandList: '/operateBasic/brandType/brand/list',
    brandSave: '/operateBasic/brandType/brand/save',
    brandEdit: '/operateBasic/brandType/brand/update',
    modelList: '/operateBasic/brandType/model/list',
    modelSave: '/operateBasic/brandType/model/save',
    modelEdit: '/operateBasic/brandType/model/update',
    getSimpleBrandTypeList: '/operateBasic/brandType/getSimpleBrandTypeList'
  },
  attributeManagement: {
    getAttributePage: '/operateBasic/attribute/getAttributePage', // 属性列表
    addAttribute: '/operateBasic/attribute/addAttribute', // 新增属性
    editAttribute: '/operateBasic/attribute/editAttribute', // 编辑属性
    getProductModelPage: '/operateBasic/productmodel/getProductModelPage', // 模型列表
    getProductModelById: '/operateBasic/productmodel/getProductModelById', // 模型详情
    addProductModel: '/operateBasic/productmodel/addProductModel', // 新增模型
    productmodelEditAttribute: '/operateBasic/productmodel/editProductModel', // 编辑模型
    // 产品单位
    getProductUnitPage: '/operateBasic/productunit/getProductUnitPage', // 产品单位列表接口
    addProductUnit: '/operateBasic/productunit/addProductUnit', // 新增产品单位接口
    editProductUnit: '/operateBasic/productunit/editProductUnit', // 编辑产品单位接口
    delProductUnit: '/operateBasic/productunit/delProductUnit', // 删除产品单位接口
    // 产品标签
    getProductLabelPage: '/operateBasic/productlabel/getProductLabelPage', // 产品标签列表接口
    addProductLabel: '/operateBasic/productlabel/addProductLabel', // 新增产品标签接口
    editProductLabel: '/operateBasic/productlabel/editProductLabel', // 编辑产品标签接口
    delProductLabel: '/operateBasic/productlabel/delProductLabel', // 删除产品标签接口
    // 产品分类
    getProductCategoryTree:
      '/operateBasic/productcategory/getProductCategoryTree', // 分类列表树接口
    addProductCategory: '/operateBasic/productcategory/addProductCategory', // 新增产品分类接口
    editProductCategory: '/operateBasic/productcategory/editProductCategory', // 编辑产品分类接口
    getProductPage: '/operateBasic/product/getProductPage',

    // 产品
    getProductById: '/operateBasic/product/getProductById', // 产品详情
    batchEnable: '/operateBasic/product/batchEnable',
    batchDisable: '/operateBasic/product/batchDisable',
    getSimpleModelList: '/operateBasic/productmodel/getSimpleModelList',
    addProduct: '/operateBasic/product/addProduct',
    editProduct: '/operateBasic/product/editProduct'
  },
  supplyChain: {
    supplierFilesList: '/operateBasic/supplierfiles/getSupplierFilesListPage', // 供应商档案列表
    supplierFilesDetail: '/operateBasic/supplierfiles/getSupplierFiles', // 供应商档案详情
    insertSupplierFiles: '/operateBasic/supplierfiles/insertSupplierFiles', // 供应商档案新增
    updateSupplierFiles: '/operateBasic/supplierfiles/updateSupplierFiles', // 供应商档案编辑
    getSupplierClassifyList:
      '/operateBasic/supplierfiles/getSupplierClassifyList', // 供应商档案_基础分类_列表
    insertSupplierClassify:
      '/operateBasic/supplierfiles/insertSupplierClassify', // 供应商档案_基础分类_新增
    updateSupplierClassify: '/operateBasic/supplierfiles/updateSupplierClassify' // 供应商档案_基础分类_编辑
  },
  demand,
};
export default apiUrl
