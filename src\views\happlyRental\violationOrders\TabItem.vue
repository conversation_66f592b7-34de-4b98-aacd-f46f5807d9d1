<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'happlyRentalViolationOrders',
  components: {
    NewHapplyRentalViolationOrders: () => import('./index.vue'),
    OldHapplyRentalViolationOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldHapplyRentalViolationOrders'
    }
  },
  created() {
    this.init(
      'OldHapplyRentalViolationOrders',
      'NewHapplyRentalViolationOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
