import { getToken } from '@/portal-common/utils/auth'
import myaxios from 'axios'
const instance = myaxios.create({
  baseURL: process.env.BASE_API5,
  timeout: 120000
})
instance.interceptors.request.use(config => {
  // console.log("config==>",config);
  config.headers['token'] = getToken()
  config.headers['withCredentials'] = true
  config.method = 'post'
  return config
}, error => {
  // console.log("error==>",error);
  return Promise.reject(error)
})
export default instance
