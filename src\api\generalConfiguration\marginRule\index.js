import { request } from '@/portal-common/api/request'
import common from '@/utils/common.js'
const { hearderData, baseUrl } = common

// 获取保证金规则列表数据
export function getOrderDepositconfigList(data, type) {
  return request({
    url: baseUrl(type) + '/config/order/depositconfig/list',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 新增保证金规则
export function createOrderDepositconfig(data, type) {
  return request({
    url: baseUrl(type) + '/config/order/depositconfig/create',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}

// 更新保证金规则
export function updateOrderDepositconfig(data, type) {
  return request({
    url: baseUrl(type) + '/config/order/depositconfig/update',
    method: 'post',
    data,
    hearderData,
    baseURL: process.env.BASE_API12,
  })
}
