<template>
  <div class="dst-detail-card-wrap" v-loading="isLock">
    <component :is="activeName"></component>
  </div>
</template>

<script>
import mixin from '@/views/common/common/mixins/orderRender.js'
export default {
  name: 'consignmentRentalValueAddedServiceOrders',
  components: {
    NewConsignmentRentalValueAddedServiceOrders: () => import('./index.vue'),
    OldConsignmentRentalValueAddedServiceOrders: () => import('./oldModel')
  },
  mixins: [mixin],
  data() {
    return {
      // 加载对应的组件
      activeName: 'OldConsignmentRentalValueAddedServiceOrders'
    }
  },
  created() {
    this.init(
      'OldConsignmentRentalValueAddedServiceOrders',
      'NewConsignmentRentalValueAddedServiceOrders'
    )
  }
}
</script>

<style lang="scss" scoped></style>
