<template>
  <div class="dst-app-container-table" v-loading="isLock">
    <dst-filtrate ref="DstFiltrate" :data-arr="setFiltrateData" @searchClick="searchClick"></dst-filtrate>
    <div class="dst-table-content">
      <sys-grid
        local-key="consignment_rental_return_car_vehicle_orders"
        :is-loading="isLock"
        :table-list="tableList"
        :table-head="setTableHeadArr"
        :total="paginationTotal"
        :page-num="pageNum"
        :page-size="pageSize"
        @pageChange="pageChange"
      />
    </div>
  </div>
</template>

<script>
import { setFiltrateData } from './functions/data'
import { setTableHeadArr } from './functions/table'
import mixin from '@/mixins/table.js'
import { changeVariableName } from '@/utils/business'
import orderMixins from '@/views/common/common/mixins/orderLink'
import selectCustomer from '@/views/common/common/mixins/selectCustomer.js'
import { addZero } from '@/utils'
import { queryOrderSearchSubItems } from '@/api/common/rental/childs.js'
import { getParams, setPageData } from '@/utils/system/business'
export default {
  name: 'NewConsignmentRentalBreachPenaltyOrders',
  mixins: [mixin, orderMixins, selectCustomer],
  data() {
    return {
      // loading加载
      isLock: false,
      // 表格数据
      tableList: [],
      // 解约违约金订单总数
      paginationTotal: 0,
      // type 当前订单类型 0：车辆租赁 1：先租后买 2：服务 3：服务 4：欢乐租 5:代收款
      type: 1
    }
  },
  computed: {
    // 解约违约金订单搜素的配置项
    setFiltrateData,
    // 解约违约金订单列表表头项
    setTableHeadArr
  },
  methods: {
    /**
     * @description:请求解约违约金订单列表数据
     */
    getTableData() {
      this.isLock = true
      const data = this.getParams()
      queryOrderSearchSubItems(data)
        .then((res) => {
          this.paginationTotal = res.data.total || 0
          this.tableList = setPageData(res)
        })
        .finally(() => {
          this.isLock = false
        })
    },
    /**
     * @description:格式化列表请求参数
     */
    getParams() {
      const params = this.$batchInputUtil(this.setFiltrateData, {
        ...this.searchData,
        parentOrderType: addZero(this.type)
      })
      changeVariableName(params, 'createTime', true, 'Time')
      this.getCustomerIds(params, false)
      return {
        pageNum: this.pageNum, //	页码
        pageSize: this.pageSize, //	每页数量
        // 1-订单基础信息、2-客户信息、3-合同信息、4.履约信息、5-商品信息、6-优惠信息、7-赠送信息、8-销售信息、9-结算信息
        ...getParams({
          data: params,
          queryItems: [1, 2, 3, 5],
          orderCategory: 13,
          // rent-租赁、sale-售车、service-服务、rent_for_sale-以租代售
          orderBusinessType: 'rent_for_sale',
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/views/common/common/styles/table.scss'
</style>
