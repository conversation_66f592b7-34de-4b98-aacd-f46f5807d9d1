﻿﻿module.exports = {
  proxy: {
    '/dst-apis': {
      // 2.0pc运营系统
      // target: 'http://172.16.8.34:8835',
      target: 'http://portal.dev.dstcar.com', // dev环境
      // target: 'http://portal.test.dstcar.com', // test
      // target: 'http://portal.uat.dstcar.com', // uat环境
      // target: 'https://eportal.dstcar.com',
      // target: 'http://172.16.8.35:8300', // dev环境
      // target: 'http://172.16.8.57:8300', // sit环境
      // target: 'http://172.16.8.46:31000', // test环境
      secure: false, // 如果是https接口，需要配置这个参数
      changeOrigin: true, // 是否跨域
      pathRewrite: {
        '^/dst-apis': '/dst-apis', // 需要rewrite的,
      },
    },
  },
}
